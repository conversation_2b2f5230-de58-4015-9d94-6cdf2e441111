"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/list.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/list.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrewTable: function() { return /* binding */ CrewTable; },\n/* harmony export */   \"default\": function() { return /* binding */ CrewList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/icons/SealogsCrewIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsCrewIcon.ts\");\n/* harmony import */ var _components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/filter/components/crew-actions */ \"(app-pages-browser)/./src/components/filter/components/crew-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,CrewTable auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CrewList(props) {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const [showActiveUsers, setShowActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__.useSidebar)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const limit = 100;\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    let [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isArchived: {\n            eq: false\n        }\n    });\n    const [trainingStatusFilter, setTrainingStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update crew duties based on active/archived state.\n    const handleSetCrewDuties = (crewDuties)=>{\n        const activeDuties = crewDuties.filter((duty)=>showActiveUsers ? !duty.archived : duty.archived);\n        const formattedCrewDuties = activeDuties.map((duty)=>{\n            return {\n                label: duty.title,\n                value: duty.id\n            };\n        });\n        setDuties(formattedCrewDuties);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getCrewDuties)(handleSetCrewDuties);\n    // Render departments recursively.\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Set vessels from vessel brief list.\n    const handleSetVessels = (vessels)=>{\n        const vesselSelection = vessels.map((vessel)=>{\n            return {\n                label: vessel.title,\n                value: vessel.id\n            };\n        });\n        setVessels(vesselSelection);\n    //loadCrewMembers()\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getVesselBriefList)(handleSetVessels);\n    const [queryCrewMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CREW_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            handleSetCrewMembers(response.readSeaLogsMembers.nodes);\n            setPageInfo(response.readSeaLogsMembers.pageInfo);\n            return response.readSeaLogsMembers.nodes;\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const handleSetCrewMembers = (crewMembers)=>{\n        const transformedCrewList = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)(crewMembers, vessels);\n        setCrewList(transformedCrewList);\n    };\n    // Function to filter crew list by training status\n    const filterCrewByTrainingStatus = (crewList, statusFilter)=>{\n        const crews = [\n            ...crewList\n        ].map((crew)=>{\n            const dues = crew.trainingStatus.dues;\n            if (dues.length === 0) {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \"Good\",\n                        dues: []\n                    }\n                };\n            } else if (dues.some((due)=>due.status.isOverdue)) {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \"Overdue\",\n                        dues: dues.filter((due)=>due.status.isOverdue)\n                    }\n                };\n            } else {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \" \",\n                        dues: dues.filter((due)=>!due.status.isOverdue)\n                    }\n                };\n            }\n        });\n        if (!statusFilter) return crews;\n        return crews.filter((crew)=>{\n            var _crew_trainingStatus, _crew_trainingStatus1;\n            const trainingStatus = (_crew_trainingStatus = crew.trainingStatus) === null || _crew_trainingStatus === void 0 ? void 0 : _crew_trainingStatus.label;\n            const dues = ((_crew_trainingStatus1 = crew.trainingStatus) === null || _crew_trainingStatus1 === void 0 ? void 0 : _crew_trainingStatus1.dues) || [];\n            if (statusFilter === \"Good\") {\n                return trainingStatus === \"Good\";\n            } else if (statusFilter === \"Overdue\") {\n                return trainingStatus === \"Overdue\";\n            } else if (statusFilter === \"Due Soon\") {\n                // Due Soon is represented by an empty string label with dues\n                // This happens when there are training sessions due within 7 days but not overdue\n                return trainingStatus === \" \" && dues.length > 0;\n            }\n            return true;\n        });\n    };\n    // Get filtered crew list for display\n    const filteredCrewList = filterCrewByTrainingStatus(crewList, trainingStatusFilter);\n    const loadCrewMembers = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        /*searchFilter.isArchived = { eq: !showActiveUsers }\r\n        const updatedFilter: SearchFilter = {\r\n            ...searchFilter,\r\n            isArchived: { eq: !showActiveUsers },\r\n        }*/ await queryCrewMembers({\n            variables: {\n                limit: limit,\n                offset: startPage * limit,\n                filter: searchFilter\n            }\n        });\n    };\n    const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"mutationUpdateUser error\", error);\n        }\n    });\n    const handleCrewDuty = async (duty, user)=>{\n        const selectedUser = {\n            ...crewList.find((crew)=>crew.ID === user.ID)\n        };\n        const newPrimaryDutyID = duty.value;\n        if (selectedUser) {\n            const updatedCrewList = crewList.map((crew)=>{\n                if (crew.ID === user.ID) {\n                    return {\n                        ...crew,\n                        PrimaryDutyID: newPrimaryDutyID\n                    };\n                }\n                return crew;\n            });\n            setCrewList(updatedCrewList);\n            // Update user\n            const variables = {\n                input: {\n                    id: +user.id,\n                    primaryDutyID: newPrimaryDutyID\n                }\n            };\n            await mutationUpdateUser({\n                variables\n            });\n        }\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadCrewMembers(newPage);\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // Handle training status filter separately since it's client-side\n        if (type === \"trainingStatus\") {\n            if (data && data.value) {\n                setTrainingStatusFilter(data.value);\n            } else {\n                setTrainingStatusFilter(null);\n            }\n            return; // Don't reload crew members for client-side filter\n        }\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vehicles = {\n                    id: {\n                        in: data.map((item)=>+item.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vehicles = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete searchFilter.vehicles;\n            }\n        }\n        if (type === \"crewDuty\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.primaryDutyID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.primaryDutyID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.primaryDutyID;\n            }\n        }\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(data.value)) {\n                searchFilter.q = {\n                    contains: data.value\n                };\n            } else {\n                delete searchFilter.q;\n            }\n        }\n        if (type === \"isArchived\") {\n            if (data !== undefined) {\n                searchFilter.isArchived = {\n                    eq: !data\n                };\n            } else {\n                delete searchFilter.isArchived;\n            }\n        }\n        setFilter(searchFilter);\n        //setPage(0)\n        loadCrewMembers(0, searchFilter);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPage(0);\n        loadCrewMembers(0, filter);\n    }, [\n        showActiveUsers,\n        filter\n    ]);\n    // Column definitions for the DataTable.\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                const fullName = \"\".concat(crewMember.firstName, \" \").concat(crewMember.surname);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex justify-start items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            size: \"sm\",\n                            variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) === \"Overdue\" ? \"destructive\" : \"success\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_12__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid min-w-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/crew/info?id=\".concat(crewMember.id),\n                                className: \"items-center truncate pl-2 text-nowrap\",\n                                children: fullName || \"--\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const fullNameA = \"\".concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.firstName, \" \").concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.surname) || \"\";\n                const fullNameB = \"\".concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.firstName, \" \").concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.surname) || \"\";\n                return fullNameA.localeCompare(fullNameB);\n            }\n        },\n        {\n            accessorKey: \"vehicles\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row gap-2 py-2.5\",\n                    children: crew.vehicles.nodes.map((vessel)=>{\n                        // Get complete vessel data with icon information\n                        const vesselWithIcon = getVesselWithIcon(vessel.id, vessel);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center text-start gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-fit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                vessel: vesselWithIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 49\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 41\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, vessel.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 37\n                            }, this)\n                        }, String(vessel.id), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 33\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vehicles_nodes_, _rowA_original_vehicles_nodes, _rowA_original_vehicles, _rowA_original, _rowB_original_vehicles_nodes_, _rowB_original_vehicles_nodes, _rowB_original_vehicles, _rowB_original;\n                const titleA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vehicles = _rowA_original.vehicles) === null || _rowA_original_vehicles === void 0 ? void 0 : (_rowA_original_vehicles_nodes = _rowA_original_vehicles.nodes) === null || _rowA_original_vehicles_nodes === void 0 ? void 0 : (_rowA_original_vehicles_nodes_ = _rowA_original_vehicles_nodes[0]) === null || _rowA_original_vehicles_nodes_ === void 0 ? void 0 : _rowA_original_vehicles_nodes_.title) || \"\";\n                const titleB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vehicles = _rowB_original.vehicles) === null || _rowB_original_vehicles === void 0 ? void 0 : (_rowB_original_vehicles_nodes = _rowB_original_vehicles.nodes) === null || _rowB_original_vehicles_nodes === void 0 ? void 0 : (_rowB_original_vehicles_nodes_ = _rowB_original_vehicles_nodes[0]) === null || _rowB_original_vehicles_nodes_ === void 0 ? void 0 : _rowB_original_vehicles_nodes_.title) || \"\";\n                return titleA.localeCompare(titleB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Primary Duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"whitespace-normal px-5\",\n                    children: crew.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_primaryDuty, _rowA_original, _rowB_original_primaryDuty, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_primaryDuty = _rowA_original.primaryDuty) === null || _rowA_original_primaryDuty === void 0 ? void 0 : _rowA_original_primaryDuty.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_primaryDuty = _rowB_original.primaryDuty) === null || _rowB_original_primaryDuty === void 0 ? void 0 : _rowB_original_primaryDuty.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: crew.trainingStatus.label === \"Overdue\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"destructive\",\n                        type: \"circle\",\n                        children: crew.trainingStatus.dues.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 21\n                }, this);\n            }\n        }\n    ];\n    const handleDropdownChange = (type, data)=>{\n        handleFilterOnChange({\n            type,\n            data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_16__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_14__.SealogsCrewIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 21\n                }, void 0),\n                title: \"All crew\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_15__.CrewFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"isArchived\", data);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                lineNumber: 482,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n                    columns: columns,\n                    data: filteredCrewList,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                lineNumber: 497,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CrewList, \"UGdz1f15cHG1jnKmgG+03+edzgE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__.useSidebar,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = CrewList;\n// ---------------------------------------------------------------------------------------//\nconst CrewTable = (param)=>{\n    let { crewList, vessels, handleCrewDuty = false, showSurname } = param;\n    _s1();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const crewListWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)(crewList, vessels);\n    const transformedCrewList = crewListWithTrainingStatus.map((crewMember)=>{\n        const filteredDues = crewMember.trainingStatus.dues.filter((due)=>{\n            return crewMember.vehicles.nodes.some((node)=>node.id === due.vesselID);\n        });\n        const updatedTrainingStatus = {\n            ...crewMember.trainingStatus,\n            dues: filteredDues\n        };\n        if (filteredDues.length === 0) {\n            updatedTrainingStatus.label = \"Good\";\n        }\n        return {\n            ...crewMember,\n            trainingStatus: updatedTrainingStatus\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ( true && typeof window.localStorage !== \"undefined\") {\n            const result = localStorage.getItem(\"admin\");\n            const admin = result === \"true\";\n            setIsAdmin(admin);\n        }\n    }, []);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2.5 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    size: \"sm\",\n                                    variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_12__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                    className: \"flex items-center pl-2 text-nowrap\",\n                                    children: [\n                                        crewMember.firstName || \"--\",\n                                        showSurname == true ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:flex\",\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 25\n                        }, undefined),\n                        handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:hidden flex-col\",\n                            children: crewMember.vehicles.nodes && crewMember.vehicles.nodes.map((vessel, index)=>{\n                                if (index < 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"max-w-32 overflow-hidden block\",\n                                            href: \"/vessel/info?id=\".concat(vessel.id),\n                                            children: vessel.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    }, vessel.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                if (index === 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-orange-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        \" \",\n                                                        crewMember.vehicles.nodes.length - 2,\n                                                        \" \",\n                                                        \"more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 57\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                                className: \"p-0 w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-full bg-background rounded\",\n                                                    children: crewMember.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    href: \"/vessel/info?id=\".concat(v.id),\n                                                                    children: v.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 85\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 81\n                                                            }, undefined)\n                                                        }, v.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 77\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 57\n                                            }, undefined)\n                                        ]\n                                    }, vessel.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                return null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && \"Vessel\"\n                }, void 0, false),\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: crew.vehicles.nodes && crew.vehicles.nodes.map((vessel, index)=>{\n                            if (index < 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-muted inline-block font-light rounded-lg p-2 border border-border m-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(vessel.id),\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 57\n                                    }, undefined)\n                                }, vessel.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            if (index === 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-orange-500\",\n                                                children: [\n                                                    \"+\",\n                                                    \" \",\n                                                    crew.vehicles.nodes.length - 2,\n                                                    \" \",\n                                                    \"more\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 57\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                            className: \"p-0 w-64\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-full bg-background rounded\",\n                                                children: crew.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: \"/vessel/info?id=\".concat(v.id),\n                                                                children: v.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 85\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 81\n                                                        }, undefined)\n                                                    }, v.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 77\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    ]\n                                }, vessel.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: \"Primary duty\",\n            cellAlignment: \"left\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-wrap text-right whitespace-normal\",\n                    children: crew.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 799,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: \"Training Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: crew.trainingStatus.label !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                        triggerType: \"hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    strokeWidth: 1,\n                                    className: \"h-9 w-9 text-destructive cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 815,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded\",\n                                        children: crew.trainingStatus.dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                            }, dueIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 829,\n                                                columnNumber: 53\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 814,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 840,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 839,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 812,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"departments\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && \"Departments\"\n                }, void 0, false),\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: crew.departments && crew.departments.nodes.length > 0 ? crew.departments.nodes.map((department)=>{\n                            var _departments_find;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/department/info?id=\".concat(department.id),\n                                className: \"flex flex-col text-nowrap\",\n                                children: (_departments_find = departments.find((dept)=>dept.id === department.id)) === null || _departments_find === void 0 ? void 0 : _departments_find.title\n                            }, department.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 868,\n                                columnNumber: 49\n                            }, undefined);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"No departments found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 883,\n                            columnNumber: 41\n                        }, undefined)\n                    }, void 0, false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 859,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n        columns: columns,\n        showToolbar: false,\n        data: transformedCrewList,\n        pageSize: 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n        lineNumber: 894,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(CrewTable, \"5wAh4WvsScd0iqV5WA++PbFH7ak=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c1 = CrewTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewList\");\n$RefreshReg$(_c1, \"CrewTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/list.tsx\n"));

/***/ })

});