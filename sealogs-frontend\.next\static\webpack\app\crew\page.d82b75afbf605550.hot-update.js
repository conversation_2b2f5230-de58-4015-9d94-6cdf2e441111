"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/list.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/list.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrewTable: function() { return /* binding */ CrewTable; },\n/* harmony export */   \"default\": function() { return /* binding */ CrewList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/icons/SealogsCrewIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsCrewIcon.ts\");\n/* harmony import */ var _components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/filter/components/crew-actions */ \"(app-pages-browser)/./src/components/filter/components/crew-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,CrewTable auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CrewList(props) {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const [showActiveUsers, setShowActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__.useSidebar)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const limit = 100;\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    let [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isArchived: {\n            eq: false\n        }\n    });\n    const [trainingStatusFilter, setTrainingStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update crew duties based on active/archived state.\n    const handleSetCrewDuties = (crewDuties)=>{\n        const activeDuties = crewDuties.filter((duty)=>showActiveUsers ? !duty.archived : duty.archived);\n        const formattedCrewDuties = activeDuties.map((duty)=>{\n            return {\n                label: duty.title,\n                value: duty.id\n            };\n        });\n        setDuties(formattedCrewDuties);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getCrewDuties)(handleSetCrewDuties);\n    // Render departments recursively.\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Set vessels from vessel brief list.\n    const handleSetVessels = (vessels)=>{\n        const vesselSelection = vessels.map((vessel)=>{\n            return {\n                label: vessel.title,\n                value: vessel.id\n            };\n        });\n        setVessels(vesselSelection);\n    //loadCrewMembers()\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getVesselBriefList)(handleSetVessels);\n    const [queryCrewMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CREW_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            handleSetCrewMembers(response.readSeaLogsMembers.nodes);\n            setPageInfo(response.readSeaLogsMembers.pageInfo);\n            return response.readSeaLogsMembers.nodes;\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const handleSetCrewMembers = (crewMembers)=>{\n        const transformedCrewList = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)(crewMembers, vessels);\n        setCrewList(transformedCrewList);\n    };\n    // Function to filter crew list by training status\n    const filterCrewByTrainingStatus = (crewList, statusFilter)=>{\n        const crews = [\n            ...crewList\n        ].map((crew1)=>{\n            const dues = crew1.trainingStatus.dues;\n            if (dues.length === 0) {\n                return {\n                    ...crew1,\n                    trainingStatus: {\n                        label: \"Good\",\n                        dues: []\n                    }\n                };\n            } else if (dues.some((due)=>due.status.isOverdue)) {\n                return {\n                    ...crew1,\n                    trainingStatus: {\n                        label: \"Overdue\",\n                        dues: dues.filter((due)=>due.status.isOverdue)\n                    }\n                };\n            } else {\n                return {\n                    ...crew1,\n                    trainingStatus: {\n                        label: \" \",\n                        dues: dues.filter((due)=>!due.status.isOverdue)\n                    }\n                };\n            }\n        });\n        if (!statusFilter) return crews;\n        return crews.filter((crew1)=>{\n            var _crew_trainingStatus, _crew_trainingStatus1;\n            const trainingStatus = (_crew_trainingStatus = crew1.trainingStatus) === null || _crew_trainingStatus === void 0 ? void 0 : _crew_trainingStatus.label;\n            const dues = ((_crew_trainingStatus1 = crew1.trainingStatus) === null || _crew_trainingStatus1 === void 0 ? void 0 : _crew_trainingStatus1.dues) || [];\n            if (statusFilter === \"Good\") {\n                return trainingStatus === \"Good\";\n            } else if (statusFilter === \"Overdue\") {\n                return trainingStatus === \"Overdue\";\n            } else if (statusFilter === \"Due Soon\") {\n                // Due Soon is represented by an empty string label with dues\n                // This happens when there are training sessions due within 7 days but not overdue\n                return trainingStatus === \" \" && dues.length > 0;\n            }\n            return true;\n        });\n    };\n    // Get filtered crew list for display\n    const filteredCrewList = filterCrewByTrainingStatus(crewList, trainingStatusFilter);\n    const loadCrewMembers = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        /*searchFilter.isArchived = { eq: !showActiveUsers }\r\n        const updatedFilter: SearchFilter = {\r\n            ...searchFilter,\r\n            isArchived: { eq: !showActiveUsers },\r\n        }*/ await queryCrewMembers({\n            variables: {\n                limit: limit,\n                offset: startPage * limit,\n                filter: searchFilter\n            }\n        });\n    };\n    const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"mutationUpdateUser error\", error);\n        }\n    });\n    const handleCrewDuty = async (duty, user)=>{\n        const selectedUser = {\n            ...crewList.find((crew1)=>crew1.ID === user.ID)\n        };\n        const newPrimaryDutyID = duty.value;\n        if (selectedUser) {\n            const updatedCrewList = crewList.map((crew1)=>{\n                if (crew1.ID === user.ID) {\n                    return {\n                        ...crew1,\n                        PrimaryDutyID: newPrimaryDutyID\n                    };\n                }\n                return crew1;\n            });\n            setCrewList(updatedCrewList);\n            // Update user\n            const variables = {\n                input: {\n                    id: +user.id,\n                    primaryDutyID: newPrimaryDutyID\n                }\n            };\n            await mutationUpdateUser({\n                variables\n            });\n        }\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadCrewMembers(newPage);\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // Handle training status filter separately since it's client-side\n        if (type === \"trainingStatus\") {\n            if (data && data.value) {\n                setTrainingStatusFilter(data.value);\n            } else {\n                setTrainingStatusFilter(null);\n            }\n            return; // Don't reload crew members for client-side filter\n        }\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vehicles = {\n                    id: {\n                        in: data.map((item)=>+item.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vehicles = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete searchFilter.vehicles;\n            }\n        }\n        if (type === \"crewDuty\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.primaryDutyID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.primaryDutyID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.primaryDutyID;\n            }\n        }\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(data.value)) {\n                searchFilter.q = {\n                    contains: data.value\n                };\n            } else {\n                delete searchFilter.q;\n            }\n        }\n        if (type === \"isArchived\") {\n            if (data !== undefined) {\n                searchFilter.isArchived = {\n                    eq: !data\n                };\n            } else {\n                delete searchFilter.isArchived;\n            }\n        }\n        setFilter(searchFilter);\n        //setPage(0)\n        loadCrewMembers(0, searchFilter);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPage(0);\n        loadCrewMembers(0, filter);\n    }, [\n        showActiveUsers,\n        filter\n    ]);\n    // Column definitions for the DataTable.\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                const fullName = \"\".concat(crewMember.firstName, \" \").concat(crewMember.surname);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex justify-start items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            size: \"sm\",\n                            variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) === \"Overdue\" ? \"destructive\" : \"success\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_12__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid min-w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                    className: \"items-center truncate text-nowrap\",\n                                    children: fullName || \"--\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-curious-blue-400 text-[10px]\",\n                                    children: crew.primaryDuty.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const fullNameA = \"\".concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.firstName, \" \").concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.surname) || \"\";\n                const fullNameB = \"\".concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.firstName, \" \").concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.surname) || \"\";\n                return fullNameA.localeCompare(fullNameB);\n            }\n        },\n        {\n            accessorKey: \"vehicles\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row gap-2 py-2.5\",\n                    children: crew1.vehicles.nodes.map((vessel)=>{\n                        // Get complete vessel data with icon information\n                        const vesselWithIcon = getVesselWithIcon(vessel.id, vessel);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center text-start gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-fit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                vessel: vesselWithIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 49\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 41\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, vessel.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 37\n                            }, this)\n                        }, String(vessel.id), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 33\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vehicles_nodes_, _rowA_original_vehicles_nodes, _rowA_original_vehicles, _rowA_original, _rowB_original_vehicles_nodes_, _rowB_original_vehicles_nodes, _rowB_original_vehicles, _rowB_original;\n                const titleA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vehicles = _rowA_original.vehicles) === null || _rowA_original_vehicles === void 0 ? void 0 : (_rowA_original_vehicles_nodes = _rowA_original_vehicles.nodes) === null || _rowA_original_vehicles_nodes === void 0 ? void 0 : (_rowA_original_vehicles_nodes_ = _rowA_original_vehicles_nodes[0]) === null || _rowA_original_vehicles_nodes_ === void 0 ? void 0 : _rowA_original_vehicles_nodes_.title) || \"\";\n                const titleB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vehicles = _rowB_original.vehicles) === null || _rowB_original_vehicles === void 0 ? void 0 : (_rowB_original_vehicles_nodes = _rowB_original_vehicles.nodes) === null || _rowB_original_vehicles_nodes === void 0 ? void 0 : (_rowB_original_vehicles_nodes_ = _rowB_original_vehicles_nodes[0]) === null || _rowB_original_vehicles_nodes_ === void 0 ? void 0 : _rowB_original_vehicles_nodes_.title) || \"\";\n                return titleA.localeCompare(titleB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Primary duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 17\n                }, this);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"whitespace-normal px-5\",\n                    children: crew1.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_primaryDuty, _rowA_original, _rowB_original_primaryDuty, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_primaryDuty = _rowA_original.primaryDuty) === null || _rowA_original_primaryDuty === void 0 ? void 0 : _rowA_original_primaryDuty.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_primaryDuty = _rowB_original.primaryDuty) === null || _rowB_original_primaryDuty === void 0 ? void 0 : _rowB_original_primaryDuty.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: crew1.trainingStatus.label === \"Overdue\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"destructive\",\n                        type: \"circle\",\n                        children: crew1.trainingStatus.dues.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 21\n                }, this);\n            }\n        }\n    ];\n    const handleDropdownChange = (type, data)=>{\n        handleFilterOnChange({\n            type,\n            data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_16__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_14__.SealogsCrewIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 21\n                }, void 0),\n                title: \"All crew\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_15__.CrewFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"isArchived\", data);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                lineNumber: 486,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n                    columns: columns,\n                    data: filteredCrewList,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                lineNumber: 501,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CrewList, \"UGdz1f15cHG1jnKmgG+03+edzgE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__.useSidebar,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = CrewList;\n// ---------------------------------------------------------------------------------------//\nconst CrewTable = (param)=>{\n    let { crewList, vessels, handleCrewDuty = false, showSurname } = param;\n    _s1();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const crewListWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)(crewList, vessels);\n    const transformedCrewList = crewListWithTrainingStatus.map((crewMember)=>{\n        const filteredDues = crewMember.trainingStatus.dues.filter((due)=>{\n            return crewMember.vehicles.nodes.some((node)=>node.id === due.vesselID);\n        });\n        const updatedTrainingStatus = {\n            ...crewMember.trainingStatus,\n            dues: filteredDues\n        };\n        if (filteredDues.length === 0) {\n            updatedTrainingStatus.label = \"Good\";\n        }\n        return {\n            ...crewMember,\n            trainingStatus: updatedTrainingStatus\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ( true && typeof window.localStorage !== \"undefined\") {\n            const result = localStorage.getItem(\"admin\");\n            const admin = result === \"true\";\n            setIsAdmin(admin);\n        }\n    }, []);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2.5 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    size: \"sm\",\n                                    variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_12__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                    className: \"flex items-center pl-2 text-nowrap\",\n                                    children: [\n                                        crewMember.firstName || \"--\",\n                                        showSurname == true ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:flex\",\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 25\n                        }, undefined),\n                        handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:hidden flex-col\",\n                            children: crewMember.vehicles.nodes && crewMember.vehicles.nodes.map((vessel, index)=>{\n                                if (index < 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"max-w-32 overflow-hidden block\",\n                                            href: \"/vessel/info?id=\".concat(vessel.id),\n                                            children: vessel.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    }, vessel.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                if (index === 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-orange-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        \" \",\n                                                        crewMember.vehicles.nodes.length - 2,\n                                                        \" \",\n                                                        \"more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 57\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                                className: \"p-0 w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-full bg-background rounded\",\n                                                    children: crewMember.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    href: \"/vessel/info?id=\".concat(v.id),\n                                                                    children: v.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 85\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 81\n                                                            }, undefined)\n                                                        }, v.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 77\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 57\n                                            }, undefined)\n                                        ]\n                                    }, vessel.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                return null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && \"Vessel\"\n                }, void 0, false),\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: crew1.vehicles.nodes && crew1.vehicles.nodes.map((vessel, index)=>{\n                            if (index < 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-muted inline-block font-light rounded-lg p-2 border border-border m-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(vessel.id),\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 57\n                                    }, undefined)\n                                }, vessel.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            if (index === 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-orange-500\",\n                                                children: [\n                                                    \"+\",\n                                                    \" \",\n                                                    crew1.vehicles.nodes.length - 2,\n                                                    \" \",\n                                                    \"more\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 57\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                            className: \"p-0 w-64\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-full bg-background rounded\",\n                                                children: crew1.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: \"/vessel/info?id=\".concat(v.id),\n                                                                children: v.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 85\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 81\n                                                        }, undefined)\n                                                    }, v.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 77\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    ]\n                                }, vessel.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: \"Primary Duty\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-wrap text-right whitespace-normal\",\n                    children: crew1.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 802,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: \"Training Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: crew1.trainingStatus.label !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                        triggerType: \"hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    strokeWidth: 1,\n                                    className: \"h-9 w-9 text-destructive cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded\",\n                                        children: crew1.trainingStatus.dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                            }, dueIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 53\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 826,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"departments\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && \"Departments\"\n                }, void 0, false),\n            cell: (param)=>{\n                let { row } = param;\n                const crew1 = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: crew1.departments && crew1.departments.nodes.length > 0 ? crew1.departments.nodes.map((department)=>{\n                            var _departments_find;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/department/info?id=\".concat(department.id),\n                                className: \"flex flex-col text-nowrap\",\n                                children: (_departments_find = departments.find((dept)=>dept.id === department.id)) === null || _departments_find === void 0 ? void 0 : _departments_find.title\n                            }, department.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 49\n                            }, undefined);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"No departments found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 41\n                        }, undefined)\n                    }, void 0, false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n        columns: columns,\n        showToolbar: false,\n        data: transformedCrewList,\n        pageSize: 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n        lineNumber: 897,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(CrewTable, \"5wAh4WvsScd0iqV5WA++PbFH7ak=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c1 = CrewTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewList\");\n$RefreshReg$(_c1, \"CrewTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/list.tsx\n"));

/***/ })

});