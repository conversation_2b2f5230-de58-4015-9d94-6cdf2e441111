"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew/list.tsx":
/*!**********************************!*\
  !*** ./src/app/ui/crew/list.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CrewTable: function() { return /* binding */ CrewTable; },\n/* harmony export */   \"default\": function() { return /* binding */ CrewList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,CircleAlert!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/lib/icons/SealogsCrewIcon */ \"(app-pages-browser)/./src/app/lib/icons/SealogsCrewIcon.ts\");\n/* harmony import */ var _components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/filter/components/crew-actions */ \"(app-pages-browser)/./src/components/filter/components/crew-actions.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,CrewTable auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CrewList(props) {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [vessels, setVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { vesselIconData, getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData)();\n    const [showActiveUsers, setShowActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__.useSidebar)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [duties, setDuties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const limit = 100;\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    let [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isArchived: {\n            eq: false\n        }\n    });\n    const [trainingStatusFilter, setTrainingStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update crew duties based on active/archived state.\n    const handleSetCrewDuties = (crewDuties)=>{\n        const activeDuties = crewDuties.filter((duty)=>showActiveUsers ? !duty.archived : duty.archived);\n        const formattedCrewDuties = activeDuties.map((duty)=>{\n            return {\n                label: duty.title,\n                value: duty.id\n            };\n        });\n        setDuties(formattedCrewDuties);\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getCrewDuties)(handleSetCrewDuties);\n    // Render departments recursively.\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    // Set vessels from vessel brief list.\n    const handleSetVessels = (vessels)=>{\n        const vesselSelection = vessels.map((vessel)=>{\n            return {\n                label: vessel.title,\n                value: vessel.id\n            };\n        });\n        setVessels(vesselSelection);\n    //loadCrewMembers()\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getVesselBriefList)(handleSetVessels);\n    const [queryCrewMembers] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.CREW_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            handleSetCrewMembers(response.readSeaLogsMembers.nodes);\n            setPageInfo(response.readSeaLogsMembers.pageInfo);\n            return response.readSeaLogsMembers.nodes;\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const handleSetCrewMembers = (crewMembers)=>{\n        const transformedCrewList = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)(crewMembers, vessels);\n        setCrewList(transformedCrewList);\n    };\n    // Function to filter crew list by training status\n    const filterCrewByTrainingStatus = (crewList, statusFilter)=>{\n        const crews = [\n            ...crewList\n        ].map((crew)=>{\n            const dues = crew.trainingStatus.dues;\n            if (dues.length === 0) {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \"Good\",\n                        dues: []\n                    }\n                };\n            } else if (dues.some((due)=>due.status.isOverdue)) {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \"Overdue\",\n                        dues: dues.filter((due)=>due.status.isOverdue)\n                    }\n                };\n            } else {\n                return {\n                    ...crew,\n                    trainingStatus: {\n                        label: \" \",\n                        dues: dues.filter((due)=>!due.status.isOverdue)\n                    }\n                };\n            }\n        });\n        if (!statusFilter) return crews;\n        return crews.filter((crew)=>{\n            var _crew_trainingStatus, _crew_trainingStatus1;\n            const trainingStatus = (_crew_trainingStatus = crew.trainingStatus) === null || _crew_trainingStatus === void 0 ? void 0 : _crew_trainingStatus.label;\n            const dues = ((_crew_trainingStatus1 = crew.trainingStatus) === null || _crew_trainingStatus1 === void 0 ? void 0 : _crew_trainingStatus1.dues) || [];\n            if (statusFilter === \"Good\") {\n                return trainingStatus === \"Good\";\n            } else if (statusFilter === \"Overdue\") {\n                return trainingStatus === \"Overdue\";\n            } else if (statusFilter === \"Due Soon\") {\n                // Due Soon is represented by an empty string label with dues\n                // This happens when there are training sessions due within 7 days but not overdue\n                return trainingStatus === \" \" && dues.length > 0;\n            }\n            return true;\n        });\n    };\n    // Get filtered crew list for display\n    const filteredCrewList = filterCrewByTrainingStatus(crewList, trainingStatusFilter);\n    const loadCrewMembers = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        /*searchFilter.isArchived = { eq: !showActiveUsers }\r\n        const updatedFilter: SearchFilter = {\r\n            ...searchFilter,\r\n            isArchived: { eq: !showActiveUsers },\r\n        }*/ await queryCrewMembers({\n            variables: {\n                limit: limit,\n                offset: startPage * limit,\n                filter: searchFilter\n            }\n        });\n    };\n    const [mutationUpdateUser] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UPDATE_USER, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"mutationUpdateUser error\", error);\n        }\n    });\n    const handleCrewDuty = async (duty, user)=>{\n        const selectedUser = {\n            ...crewList.find((crew)=>crew.ID === user.ID)\n        };\n        const newPrimaryDutyID = duty.value;\n        if (selectedUser) {\n            const updatedCrewList = crewList.map((crew)=>{\n                if (crew.ID === user.ID) {\n                    return {\n                        ...crew,\n                        PrimaryDutyID: newPrimaryDutyID\n                    };\n                }\n                return crew;\n            });\n            setCrewList(updatedCrewList);\n            // Update user\n            const variables = {\n                input: {\n                    id: +user.id,\n                    primaryDutyID: newPrimaryDutyID\n                }\n            };\n            await mutationUpdateUser({\n                variables\n            });\n        }\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadCrewMembers(newPage);\n    };\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        const searchFilter = {\n            ...filter\n        };\n        // Handle training status filter separately since it's client-side\n        if (type === \"trainingStatus\") {\n            if (data && data.value) {\n                setTrainingStatusFilter(data.value);\n            } else {\n                setTrainingStatusFilter(null);\n            }\n            return; // Don't reload crew members for client-side filter\n        }\n        if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.vehicles = {\n                    id: {\n                        in: data.map((item)=>+item.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.vehicles = {\n                    id: {\n                        contains: +data.value\n                    }\n                };\n            } else {\n                delete searchFilter.vehicles;\n            }\n        }\n        if (type === \"crewDuty\") {\n            if (Array.isArray(data) && data.length > 0) {\n                searchFilter.primaryDutyID = {\n                    in: data.map((item)=>+item.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                searchFilter.primaryDutyID = {\n                    eq: +data.value\n                };\n            } else {\n                delete searchFilter.primaryDutyID;\n            }\n        }\n        if (type === \"keyword\") {\n            if (!lodash_isEmpty__WEBPACK_IMPORTED_MODULE_6___default()(data.value)) {\n                searchFilter.q = {\n                    contains: data.value\n                };\n            } else {\n                delete searchFilter.q;\n            }\n        }\n        if (type === \"isArchived\") {\n            if (data !== undefined) {\n                searchFilter.isArchived = {\n                    eq: !data\n                };\n            } else {\n                delete searchFilter.isArchived;\n            }\n        }\n        setFilter(searchFilter);\n        //setPage(0)\n        loadCrewMembers(0, searchFilter);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPage(0);\n        loadCrewMembers(0, filter);\n    }, [\n        showActiveUsers,\n        filter\n    ]);\n    // Column definitions for the DataTable.\n    const columns = [\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                const fullName = \"\".concat(crewMember.firstName, \" \").concat(crewMember.surname);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex justify-start items-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                            size: \"sm\",\n                            variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) === \"Overdue\" ? \"destructive\" : \"success\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_12__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid min-w-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                    className: \"items-center truncate text-nowrap\",\n                                    children: fullName || \"--\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-curious-blue-400 lap text-[10px]\",\n                                    children: crewMember.primaryDuty.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original1, _rowB_original, _rowB_original1;\n                const fullNameA = \"\".concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.firstName, \" \").concat(rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.surname) || \"\";\n                const fullNameB = \"\".concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.firstName, \" \").concat(rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.surname) || \"\";\n                return fullNameA.localeCompare(fullNameB);\n            }\n        },\n        {\n            accessorKey: \"vehicles\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row gap-2 py-2.5\",\n                    children: crew.vehicles.nodes.map((vessel)=>{\n                        // Get complete vessel data with icon information\n                        const vesselWithIcon = getVesselWithIcon(vessel.id, vessel);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center text-start gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-fit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                vessel: vesselWithIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 49\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 41\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 41\n                                    }, this)\n                                ]\n                            }, vessel.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 37\n                            }, this)\n                        }, String(vessel.id), false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 33\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vehicles_nodes_, _rowA_original_vehicles_nodes, _rowA_original_vehicles, _rowA_original, _rowB_original_vehicles_nodes_, _rowB_original_vehicles_nodes, _rowB_original_vehicles, _rowB_original;\n                const titleA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vehicles = _rowA_original.vehicles) === null || _rowA_original_vehicles === void 0 ? void 0 : (_rowA_original_vehicles_nodes = _rowA_original_vehicles.nodes) === null || _rowA_original_vehicles_nodes === void 0 ? void 0 : (_rowA_original_vehicles_nodes_ = _rowA_original_vehicles_nodes[0]) === null || _rowA_original_vehicles_nodes_ === void 0 ? void 0 : _rowA_original_vehicles_nodes_.title) || \"\";\n                const titleB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vehicles = _rowB_original.vehicles) === null || _rowB_original_vehicles === void 0 ? void 0 : (_rowB_original_vehicles_nodes = _rowB_original_vehicles.nodes) === null || _rowB_original_vehicles_nodes === void 0 ? void 0 : (_rowB_original_vehicles_nodes_ = _rowB_original_vehicles_nodes[0]) === null || _rowB_original_vehicles_nodes_ === void 0 ? void 0 : _rowB_original_vehicles_nodes_.title) || \"\";\n                return titleA.localeCompare(titleB);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Primary duty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 17\n                }, this);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"whitespace-normal px-5\",\n                    children: crew.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 21\n                }, this);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_primaryDuty, _rowA_original, _rowB_original_primaryDuty, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_primaryDuty = _rowA_original.primaryDuty) === null || _rowA_original_primaryDuty === void 0 ? void 0 : _rowA_original_primaryDuty.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_primaryDuty = _rowB_original.primaryDuty) === null || _rowB_original_primaryDuty === void 0 ? void 0 : _rowB_original_primaryDuty.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_11__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 17\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: crew.trainingStatus.label === \"Overdue\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"destructive\",\n                        type: \"circle\",\n                        children: crew.trainingStatus.dues.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 29\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 33\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 29\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 21\n                }, this);\n            }\n        }\n    ];\n    const handleDropdownChange = (type, data)=>{\n        handleFilterOnChange({\n            type,\n            data\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_16__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons_SealogsCrewIcon__WEBPACK_IMPORTED_MODULE_14__.SealogsCrewIcon, {\n                    className: \"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 21\n                }, void 0),\n                title: \"All crew\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_crew_actions__WEBPACK_IMPORTED_MODULE_15__.CrewFilterActions, {\n                    onChange: (data)=>{\n                        handleDropdownChange(\"isArchived\", data);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                lineNumber: 486,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n                    columns: columns,\n                    data: filteredCrewList,\n                    pageSize: 20,\n                    onChange: handleFilterOnChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                lineNumber: 501,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CrewList, \"UGdz1f15cHG1jnKmgG+03+edzgE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_18__.useVesselIconData,\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_8__.useSidebar,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = CrewList;\n// ---------------------------------------------------------------------------------------//\nconst CrewTable = (param)=>{\n    let { crewList, vessels, handleCrewDuty = false, showSurname } = param;\n    _s1();\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [departments, setDepartments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const renderDepartment = function(departments) {\n        let parentID = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, depth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        return departments.filter((department)=>+department.parentID === parentID).flatMap((department)=>{\n            const children = renderDepartment(departments, +department.id, depth + 1);\n            const item = {\n                ...department,\n                level: depth\n            };\n            return [\n                item,\n                ...children\n            ];\n        });\n    };\n    const [readDepartments, { loading: readDepartmentsLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.ReadDepartments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readDepartments.nodes;\n            if (data) {\n                const formattedData = renderDepartment(data);\n                setDepartments(formattedData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryCrewMembers error\", error);\n        }\n    });\n    const loadDepartments = async ()=>{\n        await readDepartments();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            loadDepartments();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const crewListWithTrainingStatus = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)(crewList, vessels);\n    const transformedCrewList = crewListWithTrainingStatus.map((crewMember)=>{\n        const filteredDues = crewMember.trainingStatus.dues.filter((due)=>{\n            return crewMember.vehicles.nodes.some((node)=>node.id === due.vesselID);\n        });\n        const updatedTrainingStatus = {\n            ...crewMember.trainingStatus,\n            dues: filteredDues\n        };\n        if (filteredDues.length === 0) {\n            updatedTrainingStatus.label = \"Good\";\n        }\n        return {\n            ...crewMember,\n            trainingStatus: updatedTrainingStatus\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if ( true && typeof window.localStorage !== \"undefined\") {\n            const result = localStorage.getItem(\"admin\");\n            const admin = result === \"true\";\n            setIsAdmin(admin);\n        }\n    }, []);\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"\",\n            cell: (param)=>{\n                let { row } = param;\n                var _crewMember_trainingStatus;\n                const crewMember = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2.5 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Avatar, {\n                                    size: \"sm\",\n                                    variant: ((_crewMember_trainingStatus = crewMember.trainingStatus) === null || _crewMember_trainingStatus === void 0 ? void 0 : _crewMember_trainingStatus.label) !== \"Good\" ? \"destructive\" : \"success\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.AvatarFallback, {\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_12__.getCrewInitials)(crewMember.firstName, crewMember.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 33\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/crew/info?id=\".concat(crewMember.id),\n                                    className: \"flex items-center pl-2 text-nowrap\",\n                                    children: [\n                                        crewMember.firstName || \"--\",\n                                        showSurname == true ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 37\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:flex\",\n                                            children: [\n                                                \"\\xa0\",\n                                                crewMember.surname || \"--\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 25\n                        }, undefined),\n                        handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex md:hidden flex-col\",\n                            children: crewMember.vehicles.nodes && crewMember.vehicles.nodes.map((vessel, index)=>{\n                                if (index < 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"max-w-32 overflow-hidden block\",\n                                            href: \"/vessel/info?id=\".concat(vessel.id),\n                                            children: vessel.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    }, vessel.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                if (index === 2) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    className: \"text-orange-500\",\n                                                    children: [\n                                                        \"+\",\n                                                        \" \",\n                                                        crewMember.vehicles.nodes.length - 2,\n                                                        \" \",\n                                                        \"more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 57\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                                className: \"p-0 w-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-full bg-background rounded\",\n                                                    children: crewMember.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    href: \"/vessel/info?id=\".concat(v.id),\n                                                                    children: v.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 85\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 81\n                                                            }, undefined)\n                                                        }, v.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 77\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 57\n                                            }, undefined)\n                                        ]\n                                    }, vessel.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 53\n                                    }, undefined);\n                                }\n                                return null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && \"Vessel\"\n                }, void 0, false),\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: handleCrewDuty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: crew.vehicles.nodes && crew.vehicles.nodes.map((vessel, index)=>{\n                            if (index < 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-muted inline-block font-light rounded-lg p-2 border border-border m-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/vessel/info?id=\".concat(vessel.id),\n                                        children: vessel.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 57\n                                    }, undefined)\n                                }, vessel.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            if (index === 2) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"text-orange-500\",\n                                                children: [\n                                                    \"+\",\n                                                    \" \",\n                                                    crew.vehicles.nodes.length - 2,\n                                                    \" \",\n                                                    \"more\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 744,\n                                            columnNumber: 57\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                            className: \"p-0 w-64\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-full bg-background rounded\",\n                                                children: crew.vehicles.nodes.slice(2).map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                href: \"/vessel/info?id=\".concat(v.id),\n                                                                children: v.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 85\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 81\n                                                        }, undefined)\n                                                    }, v.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 77\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 61\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 57\n                                        }, undefined)\n                                    ]\n                                }, vessel.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 743,\n                                    columnNumber: 53\n                                }, undefined);\n                            }\n                            return null;\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            }\n        },\n        {\n            accessorKey: \"primaryDuty\",\n            header: \"Primary Duty\",\n            cellAlignment: \"left\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-wrap text-right whitespace-normal\",\n                    children: crew.primaryDuty.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 802,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"trainingStatus\",\n            header: \"Training Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: crew.trainingStatus.label !== \"Good\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.Popover, {\n                        triggerType: \"hover\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    strokeWidth: 1,\n                                    className: \"h-9 w-9 text-destructive cursor-pointer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_13__.PopoverContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-background rounded p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded\",\n                                        children: crew.trainingStatus.dues.map((item, dueIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"\".concat(item.trainingType.title, \" - \").concat(item.status.label)\n                                            }, dueIndex, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                                lineNumber: 832,\n                                                columnNumber: 53\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                        lineNumber: 826,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                        variant: \"success\",\n                        type: \"circle\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_CircleAlert_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 843,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                        lineNumber: 842,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 21\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"departments\",\n            header: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && \"Departments\"\n                }, void 0, false),\n            cell: (param)=>{\n                let { row } = param;\n                const crew = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: isAdmin && localStorage.getItem(\"useDepartment\") === \"true\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: crew.departments && crew.departments.nodes.length > 0 ? crew.departments.nodes.map((department)=>{\n                            var _departments_find;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/department/info?id=\".concat(department.id),\n                                className: \"flex flex-col text-nowrap\",\n                                children: (_departments_find = departments.find((dept)=>dept.id === department.id)) === null || _departments_find === void 0 ? void 0 : _departments_find.title\n                            }, department.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 49\n                            }, undefined);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"No departments found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 41\n                        }, undefined)\n                    }, void 0, false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_10__.DataTable, {\n        columns: columns,\n        showToolbar: false,\n        data: transformedCrewList,\n        pageSize: 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew\\\\list.tsx\",\n        lineNumber: 897,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(CrewTable, \"5wAh4WvsScd0iqV5WA++PbFH7ak=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c1 = CrewTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewList\");\n$RefreshReg$(_c1, \"CrewTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy9saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUyQztBQUMrQjtBQUNoQjtBQUM5QjtBQUM0QjtBQUs5QjtBQUVNO0FBQ1c7QUFDUztBQUNMO0FBS1o7QUFDc0M7QUFTakQ7QUFLUTtBQUN5QjtBQUNRO0FBQ2M7QUFDdkI7QUFDVjtBQUNrQjtBQUVqRCxTQUFTbUMsU0FBU0MsS0FBVTs7SUFDdkMsTUFBTUMsU0FBU3pCLDBEQUFTQTtJQUN4QixNQUFNLENBQUMwQixVQUFVQyxZQUFZLEdBQUd0QywrQ0FBUUEsQ0FBTSxFQUFFO0lBQ2hELE1BQU0sQ0FBQ3VDLFNBQVNDLFdBQVcsR0FBR3hDLCtDQUFRQSxDQUVwQyxFQUFFO0lBQ0osTUFBTSxFQUFFeUMsY0FBYyxFQUFFQyxpQkFBaUIsRUFBRSxHQUFHVCwrRUFBaUJBO0lBQy9ELE1BQU0sQ0FBQ1UsaUJBQWlCQyxtQkFBbUIsR0FBRzVDLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sRUFBRTZDLFFBQVEsRUFBRSxHQUFHakMsa0VBQVVBO0lBQy9CLE1BQU0sQ0FBQ2tDLFdBQVdDLGFBQWEsR0FBRy9DLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dELGFBQWFDLGVBQWUsR0FBR2pELCtDQUFRQSxDQUFNLEVBQUU7SUFDdEQsTUFBTSxDQUFDa0QsUUFBUUMsVUFBVSxHQUFHbkQsK0NBQVFBLENBQU0sRUFBRTtJQUM1QyxNQUFNb0QsUUFBUTtJQUNkLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHdEQsK0NBQVFBLENBQUM7UUFDckN1RCxZQUFZO1FBQ1pDLGFBQWE7UUFDYkMsaUJBQWlCO0lBQ3JCO0lBQ0EsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUczRCwrQ0FBUUEsQ0FBQztJQUNqQyxJQUFJLENBQUM0RCxRQUFRQyxVQUFVLEdBQUc3RCwrQ0FBUUEsQ0FBQztRQUMvQjhELFlBQVk7WUFBRUMsSUFBSTtRQUFNO0lBQzVCO0lBQ0EsTUFBTSxDQUFDQyxzQkFBc0JDLHdCQUF3QixHQUFHakUsK0NBQVFBLENBRTlEO0lBRUYscURBQXFEO0lBQ3JELE1BQU1rRSxzQkFBc0IsQ0FBQ0M7UUFDekIsTUFBTUMsZUFBZUQsV0FBV1AsTUFBTSxDQUFDLENBQUNTLE9BQ3BDMUIsa0JBQWtCLENBQUMwQixLQUFLQyxRQUFRLEdBQUdELEtBQUtDLFFBQVE7UUFFcEQsTUFBTUMsc0JBQXNCSCxhQUFhSSxHQUFHLENBQUMsQ0FBQ0g7WUFDMUMsT0FBTztnQkFDSEksT0FBT0osS0FBS0ssS0FBSztnQkFDakJDLE9BQU9OLEtBQUtPLEVBQUU7WUFDbEI7UUFDSjtRQUNBekIsVUFBVW9CO0lBQ2Q7SUFDQWhFLCtEQUFhQSxDQUFDMkQ7SUFFZCxrQ0FBa0M7SUFDbEMsTUFBTVcsbUJBQW1CLFNBQ3JCN0I7WUFDQThCLDRFQUFtQixHQUNuQkMseUVBQWdCO1FBRWhCLE9BQU8vQixZQUNGWSxNQUFNLENBQUMsQ0FBQ29CLGFBQW9CLENBQUNBLFdBQVdGLFFBQVEsS0FBS0EsVUFDckRHLE9BQU8sQ0FBQyxDQUFDRDtZQUNOLE1BQU1FLFdBQVdMLGlCQUNiN0IsYUFDQSxDQUFDZ0MsV0FBV0osRUFBRSxFQUNkRyxRQUFRO1lBRVosTUFBTUksT0FBTztnQkFDVCxHQUFHSCxVQUFVO2dCQUNiSSxPQUFPTDtZQUNYO1lBQ0EsT0FBTztnQkFBQ0k7bUJBQVNEO2FBQVM7UUFDOUI7SUFDUjtJQUNBLE1BQU0sQ0FBQ0csaUJBQWlCLEVBQUVDLFNBQVNDLHNCQUFzQixFQUFFLENBQUMsR0FBR3BGLDZEQUFZQSxDQUN2RUQsbUVBQWVBLEVBQ2Y7UUFDSXNGLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVNMLGVBQWUsQ0FBQ08sS0FBSztZQUMzQyxJQUFJRCxNQUFNO2dCQUNOLE1BQU1FLGdCQUFnQmhCLGlCQUFpQmM7Z0JBQ3ZDMUMsZUFBZTRDO1lBQ25CO1FBQ0o7UUFDQUMsU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtRQUM1QztJQUNKO0lBRUosTUFBTUUsa0JBQWtCO1FBQ3BCLE1BQU1aO0lBQ1Y7SUFDQXRGLGdEQUFTQSxDQUFDO1FBQ04sSUFBSStDLFdBQVc7WUFDWG1EO1lBQ0FsRCxhQUFhO1FBQ2pCO0lBQ0osR0FBRztRQUFDRDtLQUFVO0lBRWQsc0NBQXNDO0lBQ3RDLE1BQU1vRCxtQkFBbUIsQ0FBQzNEO1FBQ3RCLE1BQU00RCxrQkFBa0I1RCxRQUFRaUMsR0FBRyxDQUFDLENBQUM0QjtZQUNqQyxPQUFPO2dCQUFFM0IsT0FBTzJCLE9BQU8xQixLQUFLO2dCQUFFQyxPQUFPeUIsT0FBT3hCLEVBQUU7WUFBQztRQUNuRDtRQUNBcEMsV0FBVzJEO0lBQ1gsbUJBQW1CO0lBQ3ZCO0lBQ0ExRixvRUFBa0JBLENBQUN5RjtJQUVuQixNQUFNLENBQUNHLGlCQUFpQixHQUFHbEcsNkRBQVlBLENBQUNGLG1FQUFlQSxFQUFFO1FBQ3JEdUYsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1ZZLHFCQUFxQlosU0FBU2Esa0JBQWtCLENBQUNYLEtBQUs7WUFDdER0QyxZQUFZb0MsU0FBU2Esa0JBQWtCLENBQUNsRCxRQUFRO1lBQ2hELE9BQU9xQyxTQUFTYSxrQkFBa0IsQ0FBQ1gsS0FBSztRQUM1QztRQUNBRSxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQzVDO0lBQ0o7SUFDQSxNQUFNTyx1QkFBdUIsQ0FBQ0U7UUFDMUIsTUFBTUMsc0JBQXNCakcsK0VBQTZCQSxDQUNyRGdHLGFBQ0FqRTtRQUVKRCxZQUFZbUU7SUFDaEI7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTUMsNkJBQTZCLENBQy9CckUsVUFDQXNFO1FBRUEsTUFBTUMsUUFBUTtlQUFJdkU7U0FBUyxDQUFDbUMsR0FBRyxDQUFDLENBQUNxQztZQUM3QixNQUFNQyxPQUFPRCxLQUFLRSxjQUFjLENBQUNELElBQUk7WUFDckMsSUFBSUEsS0FBS0UsTUFBTSxLQUFLLEdBQUc7Z0JBQ25CLE9BQU87b0JBQUUsR0FBR0gsSUFBSTtvQkFBRUUsZ0JBQWdCO3dCQUFFdEMsT0FBTzt3QkFBUXFDLE1BQU0sRUFBRTtvQkFBQztnQkFBRTtZQUNsRSxPQUFPLElBQUlBLEtBQUtHLElBQUksQ0FBQyxDQUFDQyxNQUFhQSxJQUFJQyxNQUFNLENBQUNDLFNBQVMsR0FBRztnQkFDdEQsT0FBTztvQkFDSCxHQUFHUCxJQUFJO29CQUNQRSxnQkFBZ0I7d0JBQ1p0QyxPQUFPO3dCQUNQcUMsTUFBTUEsS0FBS2xELE1BQU0sQ0FBQyxDQUFDc0QsTUFBYUEsSUFBSUMsTUFBTSxDQUFDQyxTQUFTO29CQUN4RDtnQkFDSjtZQUNKLE9BQU87Z0JBQ0gsT0FBTztvQkFDSCxHQUFHUCxJQUFJO29CQUNQRSxnQkFBZ0I7d0JBQ1p0QyxPQUFPO3dCQUNQcUMsTUFBTUEsS0FBS2xELE1BQU0sQ0FBQyxDQUFDc0QsTUFBYSxDQUFDQSxJQUFJQyxNQUFNLENBQUNDLFNBQVM7b0JBQ3pEO2dCQUNKO1lBQ0o7UUFDSjtRQUNBLElBQUksQ0FBQ1QsY0FBYyxPQUFPQztRQUMxQixPQUFPQSxNQUFNaEQsTUFBTSxDQUFDLENBQUNpRDtnQkFDTUEsc0JBQ1ZBO1lBRGIsTUFBTUUsa0JBQWlCRix1QkFBQUEsS0FBS0UsY0FBYyxjQUFuQkYsMkNBQUFBLHFCQUFxQnBDLEtBQUs7WUFDakQsTUFBTXFDLE9BQU9ELEVBQUFBLHdCQUFBQSxLQUFLRSxjQUFjLGNBQW5CRiw0Q0FBQUEsc0JBQXFCQyxJQUFJLEtBQUksRUFBRTtZQUU1QyxJQUFJSCxpQkFBaUIsUUFBUTtnQkFDekIsT0FBT0ksbUJBQW1CO1lBQzlCLE9BQU8sSUFBSUosaUJBQWlCLFdBQVc7Z0JBQ25DLE9BQU9JLG1CQUFtQjtZQUM5QixPQUFPLElBQUlKLGlCQUFpQixZQUFZO2dCQUNwQyw2REFBNkQ7Z0JBQzdELGtGQUFrRjtnQkFDbEYsT0FBT0ksbUJBQW1CLE9BQU9ELEtBQUtFLE1BQU0sR0FBRztZQUNuRDtZQUVBLE9BQU87UUFDWDtJQUNKO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU1LLG1CQUFtQlgsMkJBQ3JCckUsVUFDQTJCO0lBR0osTUFBTXNELGtCQUFrQjtZQUNwQkMsNkVBQW9CLEdBQ3BCQyxnRkFBb0I7WUFBRSxHQUFHNUQsTUFBTTtRQUFDO1FBRWhDOzs7O1NBSUMsR0FDRCxNQUFNeUMsaUJBQWlCO1lBQ25Cb0IsV0FBVztnQkFDUHJFLE9BQU9BO2dCQUNQc0UsUUFBUUgsWUFBWW5FO2dCQUNwQlEsUUFBUTREO1lBQ1o7UUFDSjtJQUNKO0lBRUEsTUFBTSxDQUFDRyxtQkFBbUIsR0FBR3ZILDREQUFXQSxDQUFDRSxrRUFBV0EsRUFBRTtRQUNsRG1GLGFBQWEsS0FBTztRQUNwQkssU0FBUyxDQUFDQztZQUNOQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtRQUM5QztJQUNKO0lBRUEsTUFBTTZCLGlCQUFpQixPQUFPdkQsTUFBV3dEO1FBQ3JDLE1BQU1DLGVBQWU7WUFDakIsR0FBR3pGLFNBQVMwRixJQUFJLENBQUMsQ0FBQ2xCLE9BQWNBLEtBQUttQixFQUFFLEtBQUtILEtBQUtHLEVBQUUsQ0FBQztRQUN4RDtRQUNBLE1BQU1DLG1CQUFtQjVELEtBQUtNLEtBQUs7UUFDbkMsSUFBSW1ELGNBQWM7WUFDZCxNQUFNSSxrQkFBa0I3RixTQUFTbUMsR0FBRyxDQUFDLENBQUNxQztnQkFDbEMsSUFBSUEsS0FBS21CLEVBQUUsS0FBS0gsS0FBS0csRUFBRSxFQUFFO29CQUNyQixPQUFPO3dCQUNILEdBQUduQixJQUFJO3dCQUNQc0IsZUFBZUY7b0JBQ25CO2dCQUNKO2dCQUNBLE9BQU9wQjtZQUNYO1lBQ0F2RSxZQUFZNEY7WUFDWixjQUFjO1lBQ2QsTUFBTVQsWUFBWTtnQkFDZFcsT0FBTztvQkFDSHhELElBQUksQ0FBQ2lELEtBQUtqRCxFQUFFO29CQUNaeUQsZUFBZUo7Z0JBQ25CO1lBQ0o7WUFDQSxNQUFNTixtQkFBbUI7Z0JBQUVGO1lBQVU7UUFDekM7SUFDSjtJQUVBLE1BQU1hLHdCQUF3QixDQUFDQztRQUMzQixJQUFJQSxVQUFVLEtBQUtBLFlBQVk3RSxNQUFNO1FBQ3JDQyxRQUFRNEU7UUFDUmpCLGdCQUFnQmlCO0lBQ3BCO0lBRUEsTUFBTUMsdUJBQXVCO1lBQUMsRUFBRUMsSUFBSSxFQUFFOUMsSUFBSSxFQUFPO1FBUTdDLE1BQU02QixlQUE2QjtZQUFFLEdBQUc1RCxNQUFNO1FBQUM7UUFFL0Msa0VBQWtFO1FBQ2xFLElBQUk2RSxTQUFTLGtCQUFrQjtZQUMzQixJQUFJOUMsUUFBUUEsS0FBS2hCLEtBQUssRUFBRTtnQkFDcEJWLHdCQUF3QjBCLEtBQUtoQixLQUFLO1lBQ3RDLE9BQU87Z0JBQ0hWLHdCQUF3QjtZQUM1QjtZQUNBLFFBQU8sbURBQW1EO1FBQzlEO1FBRUEsSUFBSXdFLFNBQVMsVUFBVTtZQUNuQixJQUFJQyxNQUFNQyxPQUFPLENBQUNoRCxTQUFTQSxLQUFLcUIsTUFBTSxHQUFHLEdBQUc7Z0JBQ3hDUSxhQUFhb0IsUUFBUSxHQUFHO29CQUNwQmhFLElBQUk7d0JBQUVpRSxJQUFJbEQsS0FBS25CLEdBQUcsQ0FBQyxDQUFDVyxPQUFTLENBQUNBLEtBQUtSLEtBQUs7b0JBQUU7Z0JBQzlDO1lBQ0osT0FBTyxJQUFJZ0IsUUFBUSxDQUFDK0MsTUFBTUMsT0FBTyxDQUFDaEQsT0FBTztnQkFDckM2QixhQUFhb0IsUUFBUSxHQUFHO29CQUFFaEUsSUFBSTt3QkFBRWtFLFVBQVUsQ0FBQ25ELEtBQUtoQixLQUFLO29CQUFDO2dCQUFFO1lBQzVELE9BQU87Z0JBQ0gsT0FBTzZDLGFBQWFvQixRQUFRO1lBQ2hDO1FBQ0o7UUFDQSxJQUFJSCxTQUFTLFlBQVk7WUFDckIsSUFBSUMsTUFBTUMsT0FBTyxDQUFDaEQsU0FBU0EsS0FBS3FCLE1BQU0sR0FBRyxHQUFHO2dCQUN4Q1EsYUFBYWEsYUFBYSxHQUFHO29CQUN6QlEsSUFBSWxELEtBQUtuQixHQUFHLENBQUMsQ0FBQ1csT0FBUyxDQUFDQSxLQUFLUixLQUFLO2dCQUN0QztZQUNKLE9BQU8sSUFBSWdCLFFBQVEsQ0FBQytDLE1BQU1DLE9BQU8sQ0FBQ2hELE9BQU87Z0JBQ3JDNkIsYUFBYWEsYUFBYSxHQUFHO29CQUFFdEUsSUFBSSxDQUFDNEIsS0FBS2hCLEtBQUs7Z0JBQUM7WUFDbkQsT0FBTztnQkFDSCxPQUFPNkMsYUFBYWEsYUFBYTtZQUNyQztRQUNKO1FBQ0EsSUFBSUksU0FBUyxXQUFXO1lBQ3BCLElBQUksQ0FBQy9ILHFEQUFPQSxDQUFDaUYsS0FBS2hCLEtBQUssR0FBRztnQkFDdEI2QyxhQUFhdUIsQ0FBQyxHQUFHO29CQUFFRCxVQUFVbkQsS0FBS2hCLEtBQUs7Z0JBQUM7WUFDNUMsT0FBTztnQkFDSCxPQUFPNkMsYUFBYXVCLENBQUM7WUFDekI7UUFDSjtRQUNBLElBQUlOLFNBQVMsY0FBYztZQUN2QixJQUFJOUMsU0FBU3FELFdBQVc7Z0JBQ3BCeEIsYUFBYTFELFVBQVUsR0FBRztvQkFBRUMsSUFBSSxDQUFDNEI7Z0JBQUs7WUFDMUMsT0FBTztnQkFDSCxPQUFPNkIsYUFBYTFELFVBQVU7WUFDbEM7UUFDSjtRQUNBRCxVQUFVMkQ7UUFDVixZQUFZO1FBQ1pGLGdCQUFnQixHQUFHRTtJQUN2QjtJQUVBekgsZ0RBQVNBLENBQUM7UUFDTjRELFFBQVE7UUFDUjJELGdCQUFnQixHQUFHMUQ7SUFDdkIsR0FBRztRQUFDakI7UUFBaUJpQjtLQUFPO0lBRTVCLHdDQUF3QztJQUN4QyxNQUFNcUYsVUFBNkM7UUFDL0M7WUFDSUMsYUFBYTtZQUNiQyxRQUFRO29CQUFDLEVBQUVDLE1BQU0sRUFBbUI7cUNBQ2hDLDhEQUFDcEksb0ZBQW1CQTtvQkFBQ29JLFFBQVFBO29CQUFRMUUsT0FBTTs7Ozs7OztZQUUvQzJFLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFRUkM7Z0JBUGhCLE1BQU1BLGFBQWtCRCxJQUFJRSxRQUFRO2dCQUNwQyxNQUFNQyxXQUFXLEdBQTJCRixPQUF4QkEsV0FBV0csU0FBUyxFQUFDLEtBQXNCLE9BQW5CSCxXQUFXSSxPQUFPO2dCQUM5RCxxQkFDSSw4REFBQ0M7b0JBQUlDLFdBQVU7O3NDQUNYLDhEQUFDNUksbURBQU1BOzRCQUNINkksTUFBSzs0QkFDTEMsU0FDSVIsRUFBQUEsNkJBQUFBLFdBQVd4QyxjQUFjLGNBQXpCd0MsaURBQUFBLDJCQUEyQjlFLEtBQUssTUFBSyxZQUMvQixnQkFDQTtzQ0FFViw0RUFBQ3ZELDJEQUFjQTswQ0FDVkUsZ0VBQWVBLENBQ1ptSSxXQUFXRyxTQUFTLEVBQ3BCSCxXQUFXSSxPQUFPOzs7Ozs7Ozs7OztzQ0FJOUIsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDWCw4REFBQ3hKLGlEQUFJQTtvQ0FDRDJKLE1BQU0saUJBQStCLE9BQWRULFdBQVczRSxFQUFFO29DQUNwQ2lGLFdBQVU7OENBQ1RKLFlBQVk7Ozs7Ozs4Q0FFakIsOERBQUNHO29DQUFJQyxXQUFVOzhDQUNWTixXQUFXVSxXQUFXLENBQUN2RixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLakQ7WUFDQXdGLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBRVpELGdCQUE2QkEsaUJBRzdCQyxnQkFBNkJBO2dCQUpwQyxNQUFNQyxZQUNGLFVBQUdGLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcscUNBQUFBLGVBQWdCVCxTQUFTLEVBQUMsS0FBMkIsT0FBeEJTLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1YLFFBQVEsY0FBZFcsc0NBQUFBLGdCQUFnQlIsT0FBTyxLQUN2RDtnQkFDSixNQUFNVyxZQUNGLFVBQUdGLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1aLFFBQVEsY0FBZFkscUNBQUFBLGVBQWdCVixTQUFTLEVBQUMsS0FBMkIsT0FBeEJVLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1aLFFBQVEsY0FBZFksc0NBQUFBLGdCQUFnQlQsT0FBTyxLQUN2RDtnQkFDSixPQUFPVSxVQUFVRSxhQUFhLENBQUNEO1lBQ25DO1FBQ0o7UUFDQTtZQUNJcEIsYUFBYTtZQUNic0IsZUFBZTtZQUNmckIsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQ3BJLG9GQUFtQkE7b0JBQUNvSSxRQUFRQTtvQkFBUTFFLE9BQU07Ozs7Ozs7WUFFL0MyRSxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU16QyxPQUFPeUMsSUFBSUUsUUFBUTtnQkFFekIscUJBQ0ksOERBQUNJO29CQUFJQyxXQUFVOzhCQUNWaEQsS0FBSytCLFFBQVEsQ0FBQ2hELEtBQUssQ0FBQ3BCLEdBQUcsQ0FBQyxDQUFDNEI7d0JBQ3RCLGlEQUFpRDt3QkFDakQsTUFBTXFFLGlCQUFpQi9ILGtCQUNuQjBELE9BQU94QixFQUFFLEVBQ1R3Qjt3QkFFSixxQkFDSSw4REFBQ3dEOzRCQUVHQyxXQUFVO3NDQUNWLDRFQUFDeEksb0RBQU9BOztrREFDSiw4REFBQ0UsMkRBQWNBO2tEQUNYLDRFQUFDcUk7NENBQUlDLFdBQVU7c0RBQ1gsNEVBQUM3SCw0REFBVUE7Z0RBQ1BvRSxRQUFRcUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSXBCLDhEQUFDbkosMkRBQWNBO2tEQUNWOEUsT0FBTzFCLEtBQUs7Ozs7Ozs7K0JBVFAwQixPQUFPeEIsRUFBRTs7Ozs7MkJBRmxCOEYsT0FBT3RFLE9BQU94QixFQUFFOzs7OztvQkFnQmpDOzs7Ozs7WUFHWjtZQUNBc0YsV0FBVyxDQUFDQyxNQUFXQztvQkFDSkQsZ0NBQUFBLCtCQUFBQSx5QkFBQUEsZ0JBQ0FDLGdDQUFBQSwrQkFBQUEseUJBQUFBO2dCQURmLE1BQU1PLFNBQVNSLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcsc0NBQUFBLDBCQUFBQSxlQUFnQnZCLFFBQVEsY0FBeEJ1QiwrQ0FBQUEsZ0NBQUFBLHdCQUEwQnZFLEtBQUssY0FBL0J1RSxxREFBQUEsaUNBQUFBLDZCQUFpQyxDQUFDLEVBQUUsY0FBcENBLHFEQUFBQSwrQkFBc0N6RixLQUFLLEtBQUk7Z0JBQzlELE1BQU1rRyxTQUFTUixDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNWixRQUFRLGNBQWRZLHNDQUFBQSwwQkFBQUEsZUFBZ0J4QixRQUFRLGNBQXhCd0IsK0NBQUFBLGdDQUFBQSx3QkFBMEJ4RSxLQUFLLGNBQS9Cd0UscURBQUFBLGlDQUFBQSw2QkFBaUMsQ0FBQyxFQUFFLGNBQXBDQSxxREFBQUEsK0JBQXNDMUYsS0FBSyxLQUFJO2dCQUU5RCxPQUFPaUcsT0FBT0osYUFBYSxDQUFDSztZQUNoQztRQUNKO1FBQ0E7WUFDSTFCLGFBQWE7WUFDYnNCLGVBQWU7WUFDZnJCLFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUNwSSxvRkFBbUJBO29CQUFDb0ksUUFBUUE7b0JBQVExRSxPQUFNOzs7Ozs7O1lBRS9DbUcsWUFBWTtZQUNaeEIsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNekMsT0FBT3lDLElBQUlFLFFBQVE7Z0JBQ3pCLHFCQUNJLDhEQUFDSTtvQkFBSUMsV0FBVTs4QkFDVmhELEtBQUtvRCxXQUFXLENBQUN2RixLQUFLOzs7Ozs7WUFHbkM7WUFDQXdGLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBQ0pELDRCQUFBQSxnQkFDQUMsNEJBQUFBO2dCQURmLE1BQU1VLFNBQVNYLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1YLFFBQVEsY0FBZFcsc0NBQUFBLDZCQUFBQSxlQUFnQkYsV0FBVyxjQUEzQkUsaURBQUFBLDJCQUE2QnpGLEtBQUssS0FBSTtnQkFDckQsTUFBTXFHLFNBQVNYLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1aLFFBQVEsY0FBZFksc0NBQUFBLDZCQUFBQSxlQUFnQkgsV0FBVyxjQUEzQkcsaURBQUFBLDJCQUE2QjFGLEtBQUssS0FBSTtnQkFDckQsT0FBT29HLE9BQU9QLGFBQWEsQ0FBQ1E7WUFDaEM7UUFDSjtRQUNBO1lBQ0k3QixhQUFhO1lBQ2JDLFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUNRO29CQUFJQyxXQUFVOzhCQUNYLDRFQUFDN0ksb0ZBQW1CQTt3QkFBQ29JLFFBQVFBO3dCQUFRMUUsT0FBTTs7Ozs7Ozs7Ozs7O1lBR25EMkUsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNekMsT0FBT3lDLElBQUlFLFFBQVE7Z0JBQ3pCLHFCQUNJLDhEQUFDSTtvQkFBSUMsV0FBVTs4QkFDVmhELEtBQUtFLGNBQWMsQ0FBQ3RDLEtBQUssS0FBSywwQkFDM0IsOERBQUN0RCxrREFBS0E7d0JBQUM0SSxTQUFRO3dCQUFjdEIsTUFBSztrQ0FDN0I1QixLQUFLRSxjQUFjLENBQUNELElBQUksQ0FBQ0UsTUFBTTs7Ozs7NkNBR3BDLDhEQUFDN0Ysa0RBQUtBO3dCQUFDNEksU0FBUTt3QkFBVXRCLE1BQUs7a0NBQzFCLDRFQUFDN0csOEZBQUtBOzRCQUFDaUksV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztZQUtyQztRQUNKO0tBQ0g7SUFFRCxNQUFNbUIsdUJBQXVCLENBQUN2QyxNQUFjOUM7UUFDeEM2QyxxQkFBcUI7WUFBRUM7WUFBTTlDO1FBQUs7SUFDdEM7SUFFQSxxQkFDSTs7MEJBQ0ksOERBQUM1RCxtRUFBVUE7Z0JBQ1BrSixvQkFDSSw4REFBQ3BKLDRFQUFlQTtvQkFDWmdJLFdBQVk7Ozs7OztnQkFHcEJuRixPQUFNO2dCQUNOd0csdUJBQ0ksOERBQUNwSiwwRkFBaUJBO29CQUNkcUosVUFBVSxDQUFDeEY7d0JBQ1BxRixxQkFBcUIsY0FBY3JGO29CQUN2Qzs7Ozs7Ozs7Ozs7MEJBSVosOERBQUNpRTtnQkFBSUMsV0FBVTswQkFDWCw0RUFBQzlJLGlFQUFTQTtvQkFDTmtJLFNBQVNBO29CQUNUdEQsTUFBTTBCO29CQUNOK0QsVUFBVTtvQkFDVkQsVUFBVTNDOzs7Ozs7Ozs7Ozs7O0FBSzlCO0dBbGR3QnRHOztRQUNMdkIsc0RBQVNBO1FBS3NCc0IsMkVBQWlCQTtRQUUxQ3JCLDhEQUFVQTtRQXNEZ0NULHlEQUFZQTtRQW9DaERBLHlEQUFZQTtRQXlGVkMsd0RBQVdBOzs7S0EzTHBCOEI7QUFtZHhCLDRGQUE0RjtBQUNyRixNQUFNbUosWUFBWTtRQUFDLEVBQ3RCaEosUUFBUSxFQUNSRSxPQUFPLEVBQ1BxRixpQkFBaUIsS0FBSyxFQUN0QjBELFdBQVcsRUFDVDs7SUFDRixNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR3hMLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ2dELGFBQWFDLGVBQWUsR0FBR2pELCtDQUFRQSxDQUFDLEVBQUU7SUFDakQsTUFBTSxDQUFDOEMsV0FBV0MsYUFBYSxHQUFHL0MsK0NBQVFBLENBQUM7SUFDM0MsTUFBTTZFLG1CQUFtQixTQUNyQjdCO1lBQ0E4Qiw0RUFBbUIsR0FDbkJDLHlFQUFnQjtRQUVoQixPQUFPL0IsWUFDRlksTUFBTSxDQUFDLENBQUNvQixhQUFvQixDQUFDQSxXQUFXRixRQUFRLEtBQUtBLFVBQ3JERyxPQUFPLENBQUMsQ0FBQ0Q7WUFDTixNQUFNRSxXQUFXTCxpQkFDYjdCLGFBQ0EsQ0FBQ2dDLFdBQVdKLEVBQUUsRUFDZEcsUUFBUTtZQUVaLE1BQU1JLE9BQU87Z0JBQ1QsR0FBR0gsVUFBVTtnQkFDYkksT0FBT0w7WUFDWDtZQUNBLE9BQU87Z0JBQUNJO21CQUFTRDthQUFTO1FBQzlCO0lBQ1I7SUFDQSxNQUFNLENBQUNHLGlCQUFpQixFQUFFQyxTQUFTQyxzQkFBc0IsRUFBRSxDQUFDLEdBQUdwRiw2REFBWUEsQ0FDdkVELG1FQUFlQSxFQUNmO1FBQ0lzRixhQUFhO1FBQ2JDLGFBQWEsQ0FBQ0M7WUFDVixNQUFNQyxPQUFPRCxTQUFTTCxlQUFlLENBQUNPLEtBQUs7WUFDM0MsSUFBSUQsTUFBTTtnQkFDTixNQUFNRSxnQkFBZ0JoQixpQkFBaUJjO2dCQUN2QzFDLGVBQWU0QztZQUNuQjtRQUNKO1FBQ0FDLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDNUM7SUFDSjtJQUVKLE1BQU1FLGtCQUFrQjtRQUNwQixNQUFNWjtJQUNWO0lBQ0F0RixnREFBU0EsQ0FBQztRQUNOLElBQUkrQyxXQUFXO1lBQ1htRDtZQUNBbEQsYUFBYTtRQUNqQjtJQUNKLEdBQUc7UUFBQ0Q7S0FBVTtJQUNkLE1BQU0ySSw2QkFBNkJqTCwrRUFBNkJBLENBQzVENkIsVUFDQUU7SUFFSixNQUFNa0Usc0JBQXNCZ0YsMkJBQTJCakgsR0FBRyxDQUN0RCxDQUFDK0U7UUFDRyxNQUFNbUMsZUFBZW5DLFdBQVd4QyxjQUFjLENBQUNELElBQUksQ0FBQ2xELE1BQU0sQ0FDdEQsQ0FBQ3NEO1lBQ0csT0FBT3FDLFdBQVdYLFFBQVEsQ0FBQ2hELEtBQUssQ0FBQ3FCLElBQUksQ0FDakMsQ0FBQzBFLE9BQWNBLEtBQUsvRyxFQUFFLEtBQUtzQyxJQUFJMEUsUUFBUTtRQUUvQztRQUVKLE1BQU1DLHdCQUF3QjtZQUMxQixHQUFHdEMsV0FBV3hDLGNBQWM7WUFDNUJELE1BQU00RTtRQUNWO1FBQ0EsSUFBSUEsYUFBYTFFLE1BQU0sS0FBSyxHQUFHO1lBQzNCNkUsc0JBQXNCcEgsS0FBSyxHQUFHO1FBQ2xDO1FBQ0EsT0FBTztZQUFFLEdBQUc4RSxVQUFVO1lBQUV4QyxnQkFBZ0I4RTtRQUFzQjtJQUNsRTtJQUdKOUwsZ0RBQVNBLENBQUM7UUFDTixJQUNJLEtBQWtCLElBQ2xCLE9BQU8rTCxPQUFPQyxZQUFZLEtBQUssYUFDakM7WUFDRSxNQUFNQyxTQUFTRCxhQUFhRSxPQUFPLENBQUM7WUFDcEMsTUFBTUMsUUFBUUYsV0FBVztZQUN6QlIsV0FBV1U7UUFDZjtJQUNKLEdBQUcsRUFBRTtJQUVMLE1BQU1qRCxVQUFVbkkseUVBQWFBLENBQUM7UUFDMUI7WUFDSW9JLGFBQWE7WUFDYkMsUUFBUTtZQUNSRSxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7b0JBUUpDO2dCQVBwQixNQUFNQSxhQUFrQkQsSUFBSUUsUUFBUTtnQkFDcEMscUJBQ0ksOERBQUNJOztzQ0FDRyw4REFBQ0E7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDNUksbURBQU1BO29DQUNINkksTUFBSztvQ0FDTEMsU0FDSVIsRUFBQUEsNkJBQUFBLFdBQVd4QyxjQUFjLGNBQXpCd0MsaURBQUFBLDJCQUEyQjlFLEtBQUssTUFBSyxTQUMvQixnQkFDQTs4Q0FFViw0RUFBQ3ZELDJEQUFjQTtrREFDVkUsZ0VBQWVBLENBQ1ptSSxXQUFXRyxTQUFTLEVBQ3BCSCxXQUFXSSxPQUFPOzs7Ozs7Ozs7Ozs4Q0FJOUIsOERBQUN0SixpREFBSUE7b0NBQ0QySixNQUFNLGlCQUErQixPQUFkVCxXQUFXM0UsRUFBRTtvQ0FDcENpRixXQUFVOzt3Q0FDVE4sV0FBV0csU0FBUyxJQUFJO3dDQUN4QjRCLGVBQWUscUJBQ1osOERBQUNhOztnREFBSztnREFFRDVDLFdBQVdJLE9BQU8sSUFBSTs7Ozs7O3NFQUczQiw4REFBQ3dDOzRDQUFLdEMsV0FBVTs7Z0RBQWlCO2dEQUU1Qk4sV0FBV0ksT0FBTyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU10Qy9CLGdDQUNHLDhEQUFDZ0M7NEJBQUlDLFdBQVU7c0NBQ1ZOLFdBQVdYLFFBQVEsQ0FBQ2hELEtBQUssSUFDdEIyRCxXQUFXWCxRQUFRLENBQUNoRCxLQUFLLENBQUNwQixHQUFHLENBQ3pCLENBQUM0QixRQUFhZ0c7Z0NBQ1YsSUFBSUEsUUFBUSxHQUFHO29DQUNYLHFCQUNJLDhEQUFDeEM7d0NBRUdDLFdBQVU7a0RBQ1YsNEVBQUN4SixpREFBSUE7NENBQ0R3SixXQUFVOzRDQUNWRyxNQUFNLG1CQUE2QixPQUFWNUQsT0FBT3hCLEVBQUU7c0RBQ2pDd0IsT0FBTzFCLEtBQUs7Ozs7Ozt1Q0FMWjBCLE9BQU94QixFQUFFOzs7OztnQ0FTMUI7Z0NBQ0EsSUFBSXdILFVBQVUsR0FBRztvQ0FDYixxQkFDSSw4REFBQzVLLDREQUFPQTs7MERBQ0osOERBQUNFLG1FQUFjQTtnREFBQzJLLE9BQU87MERBQ25CLDRFQUFDeEwseURBQU1BO29EQUNIa0osU0FBUTtvREFDUkQsTUFBSztvREFDTEQsV0FBVTs7d0RBQWtCO3dEQUMxQjt3REFDRE4sV0FDSVgsUUFBUSxDQUNSaEQsS0FBSyxDQUNMb0IsTUFBTSxHQUNQO3dEQUFHO3dEQUFJOzs7Ozs7Ozs7Ozs7MERBSW5CLDhEQUFDdkYsbUVBQWNBO2dEQUFDb0ksV0FBVTswREFDdEIsNEVBQUNEO29EQUFJQyxXQUFVOzhEQUNWTixXQUFXWCxRQUFRLENBQUNoRCxLQUFLLENBQ3JCMEcsS0FBSyxDQUFDLEdBQ045SCxHQUFHLENBQ0EsQ0FDSStILGtCQUVBLDhEQUFDM0M7NERBSUdDLFdBQVU7c0VBQ1YsNEVBQUNEO2dFQUFJQyxXQUFVOzBFQUNYLDRFQUFDeEosaURBQUlBO29FQUNEMkosTUFBTSxtQkFBd0IsT0FBTHVDLEVBQUUzSCxFQUFFOzhFQUV6QjJILEVBQUU3SCxLQUFLOzs7Ozs7Ozs7OzsyREFQZjZILEVBQUUzSCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3VDQXpCdEJ3QixPQUFPeEIsRUFBRTs7Ozs7Z0NBMkMvQjtnQ0FDQSxPQUFPOzRCQUNYOzs7Ozs7Ozs7Ozs7WUFNNUI7UUFDSjtRQUNBO1lBQ0lzRSxhQUFhO1lBQ2JDLFFBQVEsa0JBQU07OEJBQUd2QixrQkFBa0I7O1lBQ25DNEMsZUFBZTtZQUNmbkIsTUFBTTtvQkFBQyxFQUFFQyxHQUFHLEVBQWdCO2dCQUN4QixNQUFNekMsT0FBT3lDLElBQUlFLFFBQVE7Z0JBQ3pCLHFCQUNJOzhCQUNLNUIsZ0NBQ0csOERBQUNnQztrQ0FDSS9DLEtBQUsrQixRQUFRLENBQUNoRCxLQUFLLElBQ2hCaUIsS0FBSytCLFFBQVEsQ0FBQ2hELEtBQUssQ0FBQ3BCLEdBQUcsQ0FDbkIsQ0FBQzRCLFFBQWFnRzs0QkFDVixJQUFJQSxRQUFRLEdBQUc7Z0NBQ1gscUJBQ0ksOERBQUN4QztvQ0FFR0MsV0FBVTs4Q0FDViw0RUFBQ3hKLGlEQUFJQTt3Q0FDRDJKLE1BQU0sbUJBQTZCLE9BQVY1RCxPQUFPeEIsRUFBRTtrREFDakN3QixPQUFPMUIsS0FBSzs7Ozs7O21DQUpaMEIsT0FBT3hCLEVBQUU7Ozs7OzRCQVExQjs0QkFDQSxJQUFJd0gsVUFBVSxHQUFHO2dDQUNiLHFCQUNJLDhEQUFDNUssNERBQU9BOztzREFDSiw4REFBQ0UsbUVBQWNBOzRDQUFDMkssT0FBTztzREFDbkIsNEVBQUN4TCx5REFBTUE7Z0RBQ0hrSixTQUFRO2dEQUNSRCxNQUFLO2dEQUNMRCxXQUFVOztvREFBa0I7b0RBQzFCO29EQUNEaEQsS0FBSytCLFFBQVEsQ0FDVGhELEtBQUssQ0FDTG9CLE1BQU0sR0FDUDtvREFBRztvREFBSTs7Ozs7Ozs7Ozs7O3NEQUluQiw4REFBQ3ZGLG1FQUFjQTs0Q0FBQ29JLFdBQVU7c0RBQ3RCLDRFQUFDRDtnREFBSUMsV0FBVTswREFDVmhELEtBQUsrQixRQUFRLENBQUNoRCxLQUFLLENBQ2YwRyxLQUFLLENBQUMsR0FDTjlILEdBQUcsQ0FDQSxDQUNJK0gsa0JBRUEsOERBQUMzQzt3REFJR0MsV0FBVTtrRUFDViw0RUFBQ0Q7NERBQUlDLFdBQVU7c0VBQ1gsNEVBQUN4SixpREFBSUE7Z0VBQ0QySixNQUFNLG1CQUF3QixPQUFMdUMsRUFBRTNILEVBQUU7MEVBRXpCMkgsRUFBRTdILEtBQUs7Ozs7Ozs7Ozs7O3VEQVBmNkgsRUFBRTNILEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBeEJ0QndCLE9BQU94QixFQUFFOzs7Ozs0QkEwQy9COzRCQUNBLE9BQU87d0JBQ1g7Ozs7Ozs7WUFNNUI7UUFDSjtRQUNBO1lBQ0lzRSxhQUFhO1lBQ2JDLFFBQVE7WUFDUnFCLGVBQWU7WUFDZm5CLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTXpDLE9BQU95QyxJQUFJRSxRQUFRO2dCQUN6QixxQkFDSSw4REFBQ0k7b0JBQUlDLFdBQVU7OEJBQ1ZoRCxLQUFLb0QsV0FBVyxDQUFDdkYsS0FBSzs7Ozs7O1lBR25DO1FBQ0o7UUFDQTtZQUNJd0UsYUFBYTtZQUNiQyxRQUFRO1lBQ1JFLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTXpDLE9BQU95QyxJQUFJRSxRQUFRO2dCQUV6QixxQkFDSSw4REFBQ0k7b0JBQUlDLFdBQVU7OEJBQ1ZoRCxLQUFLRSxjQUFjLENBQUN0QyxLQUFLLEtBQUssdUJBQzNCLDhEQUFDakQsNERBQU9BO3dCQUFDZ0wsYUFBWTs7MENBQ2pCLDhEQUFDOUssbUVBQWNBO2dDQUFDMkssT0FBTzswQ0FDbkIsNEVBQUMxSyw4RkFBV0E7b0NBQ1I4SyxhQUFhO29DQUNiNUMsV0FBVTs7Ozs7Ozs7Ozs7MENBR2xCLDhEQUFDcEksbUVBQWNBOzBDQUNYLDRFQUFDbUk7b0NBQUlDLFdBQVU7OENBQ1gsNEVBQUNEO3dDQUFJQyxXQUFVO2tEQUNWaEQsS0FBS0UsY0FBYyxDQUFDRCxJQUFJLENBQUN0QyxHQUFHLENBQ3pCLENBQ0lXLE1BQ0F1SCx5QkFFQSw4REFBQzlDOzBEQUNJLEdBQWdDekUsT0FBN0JBLEtBQUt3SCxZQUFZLENBQUNqSSxLQUFLLEVBQUMsT0FBdUIsT0FBbEJTLEtBQUtnQyxNQUFNLENBQUMxQyxLQUFLOytDQUQ1Q2lJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQVVsQyw4REFBQ3ZMLGtEQUFLQTt3QkFBQzRJLFNBQVE7d0JBQVV0QixNQUFLO2tDQUMxQiw0RUFBQzdHLDhGQUFLQTs0QkFBQ2lJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLckM7UUFDSjtRQUNBO1lBQ0lYLGFBQWE7WUFDYkMsUUFBUSxrQkFDSjs4QkFDS29DLFdBQ0dRLGFBQWFFLE9BQU8sQ0FBQyxxQkFBcUIsVUFDMUM7O1lBR1o1QyxNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU16QyxPQUFPeUMsSUFBSUUsUUFBUTtnQkFDekIscUJBQ0ksOERBQUNJOzhCQUNJMkIsV0FDR1EsYUFBYUUsT0FBTyxDQUFDLHFCQUNqQix3QkFDQTtrQ0FDS3BGLEtBQUs3RCxXQUFXLElBQ2pCNkQsS0FBSzdELFdBQVcsQ0FBQzRDLEtBQUssQ0FBQ29CLE1BQU0sR0FBRyxJQUM1QkgsS0FBSzdELFdBQVcsQ0FBQzRDLEtBQUssQ0FBQ3BCLEdBQUcsQ0FDdEIsQ0FBQ1E7Z0NBTVdoQztpREFMUiw4REFBQzNDLGlEQUFJQTtnQ0FFRDJKLE1BQU0sdUJBQXFDLE9BQWRoRixXQUFXSixFQUFFO2dDQUMxQ2lGLFdBQVU7MkNBRU43RyxvQkFBQUEsWUFBWStFLElBQUksQ0FDWixDQUFDNkUsT0FDR0EsS0FBS2hJLEVBQUUsS0FDUEksV0FBV0osRUFBRSxlQUhyQjVCLHdDQUFBQSxrQkFJRzBCLEtBQUs7K0JBUlBNLFdBQVdKLEVBQUU7Ozs7OzJDQWM5Qiw4REFBQ3VIO3NDQUFLOzs7Ozs7Ozs7Ozs7WUFNbEM7UUFDSjtLQUNIO0lBRUQscUJBQ0ksOERBQUNwTCxpRUFBU0E7UUFDTmtJLFNBQVNBO1FBQ1Q0RCxhQUFhO1FBQ2JsSCxNQUFNYztRQUNOMkUsVUFBVTs7Ozs7O0FBR3RCLEVBQUM7SUF2WVlDOztRQTZCc0RsTCx5REFBWUE7OztNQTdCbEVrTCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXcvbGlzdC50c3g/YTJlMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgQ1JFV19CUklFRl9MSVNULCBSZWFkRGVwYXJ0bWVudHMgfSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9xdWVyeSdcclxuaW1wb3J0IHsgdXNlTGF6eVF1ZXJ5LCB1c2VNdXRhdGlvbiB9IGZyb20gJ0BhcG9sbG8vY2xpZW50J1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXHJcbmltcG9ydCB7IFVQREFURV9VU0VSIH0gZnJvbSAnQC9hcHAvbGliL2dyYXBoUUwvbXV0YXRpb24nXHJcbmltcG9ydCB7XHJcbiAgICBnZXRDcmV3RHV0aWVzLFxyXG4gICAgR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMsXHJcbiAgICBnZXRWZXNzZWxCcmllZkxpc3QsXHJcbn0gZnJvbSAnQC9hcHAvbGliL2FjdGlvbnMnXHJcblxyXG5pbXBvcnQgeyBpc0VtcHR5IH0gZnJvbSAnbG9kYXNoJ1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcbmltcG9ydCB7IHVzZVNpZGViYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2lkZWJhcidcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcclxuaW1wb3J0IHtcclxuICAgIGNyZWF0ZUNvbHVtbnMsXHJcbiAgICBEYXRhVGFibGUsXHJcbiAgICBFeHRlbmRlZENvbHVtbkRlZixcclxufSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyZWRUYWJsZSdcclxuaW1wb3J0IHsgRGF0YVRhYmxlU29ydEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXRhLXRhYmxlLXNvcnQtaGVhZGVyJ1xyXG5pbXBvcnQge1xyXG4gICAgQXZhdGFyLFxyXG4gICAgQXZhdGFyRmFsbGJhY2ssXHJcbiAgICBCYWRnZSxcclxuICAgIGdldENyZXdJbml0aWFscyxcclxuICAgIFRvb2x0aXAsXHJcbiAgICBUb29sdGlwQ29udGVudCxcclxuICAgIFRvb2x0aXBUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aSdcclxuaW1wb3J0IHtcclxuICAgIFBvcG92ZXIsXHJcbiAgICBQb3BvdmVyQ29udGVudCxcclxuICAgIFBvcG92ZXJUcmlnZ2VyLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS9wb3BvdmVyJ1xyXG5pbXBvcnQgeyBDaXJjbGVBbGVydCwgQ2hlY2ssIExhcHRvcCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcclxuaW1wb3J0IHsgU2VhbG9nc0NyZXdJY29uIH0gZnJvbSAnQC9hcHAvbGliL2ljb25zL1NlYWxvZ3NDcmV3SWNvbidcclxuaW1wb3J0IHsgQ3Jld0ZpbHRlckFjdGlvbnMgfSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyL2NvbXBvbmVudHMvY3Jldy1hY3Rpb25zJ1xyXG5pbXBvcnQgeyBMaXN0SGVhZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xpc3QtaGVhZGVyJ1xyXG5pbXBvcnQgVmVzc2VsSWNvbiBmcm9tICcuLi92ZXNzZWxzL3Zlc2VsLWljb24nXHJcbmltcG9ydCB7IHVzZVZlc3NlbEljb25EYXRhIH0gZnJvbSAnQC9hcHAvbGliL3Zlc3NlbC1pY29uLWhlbHBlcidcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENyZXdMaXN0KHByb3BzOiBhbnkpIHtcclxuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgICBjb25zdCBbY3Jld0xpc3QsIHNldENyZXdMaXN0XSA9IHVzZVN0YXRlPGFueT4oW10pXHJcbiAgICBjb25zdCBbdmVzc2Vscywgc2V0VmVzc2Vsc10gPSB1c2VTdGF0ZTxcclxuICAgICAgICBBcnJheTx7IGxhYmVsOiBzdHJpbmc7IHZhbHVlOiBudW1iZXIgfT5cclxuICAgID4oW10pXHJcbiAgICBjb25zdCB7IHZlc3NlbEljb25EYXRhLCBnZXRWZXNzZWxXaXRoSWNvbiB9ID0gdXNlVmVzc2VsSWNvbkRhdGEoKVxyXG4gICAgY29uc3QgW3Nob3dBY3RpdmVVc2Vycywgc2V0U2hvd0FjdGl2ZVVzZXJzXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgICBjb25zdCB7IGlzTW9iaWxlIH0gPSB1c2VTaWRlYmFyKClcclxuICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gICAgY29uc3QgW2RlcGFydG1lbnRzLCBzZXREZXBhcnRtZW50c10gPSB1c2VTdGF0ZTxhbnk+KFtdKVxyXG4gICAgY29uc3QgW2R1dGllcywgc2V0RHV0aWVzXSA9IHVzZVN0YXRlPGFueT4oW10pXHJcbiAgICBjb25zdCBsaW1pdCA9IDEwMFxyXG4gICAgY29uc3QgW3BhZ2VJbmZvLCBzZXRQYWdlSW5mb10gPSB1c2VTdGF0ZSh7XHJcbiAgICAgICAgdG90YWxDb3VudDogMCxcclxuICAgICAgICBoYXNOZXh0UGFnZTogZmFsc2UsXHJcbiAgICAgICAgaGFzUHJldmlvdXNQYWdlOiBmYWxzZSxcclxuICAgIH0pXHJcbiAgICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZSgwKVxyXG4gICAgbGV0IFtmaWx0ZXIsIHNldEZpbHRlcl0gPSB1c2VTdGF0ZSh7XHJcbiAgICAgICAgaXNBcmNoaXZlZDogeyBlcTogZmFsc2UgfSxcclxuICAgIH0gYXMgU2VhcmNoRmlsdGVyKVxyXG4gICAgY29uc3QgW3RyYWluaW5nU3RhdHVzRmlsdGVyLCBzZXRUcmFpbmluZ1N0YXR1c0ZpbHRlcl0gPSB1c2VTdGF0ZTxcclxuICAgICAgICBzdHJpbmcgfCBudWxsXHJcbiAgICA+KG51bGwpXHJcblxyXG4gICAgLy8gVXBkYXRlIGNyZXcgZHV0aWVzIGJhc2VkIG9uIGFjdGl2ZS9hcmNoaXZlZCBzdGF0ZS5cclxuICAgIGNvbnN0IGhhbmRsZVNldENyZXdEdXRpZXMgPSAoY3Jld0R1dGllczogYW55KSA9PiB7XHJcbiAgICAgICAgY29uc3QgYWN0aXZlRHV0aWVzID0gY3Jld0R1dGllcy5maWx0ZXIoKGR1dHk6IGFueSkgPT5cclxuICAgICAgICAgICAgc2hvd0FjdGl2ZVVzZXJzID8gIWR1dHkuYXJjaGl2ZWQgOiBkdXR5LmFyY2hpdmVkLFxyXG4gICAgICAgIClcclxuICAgICAgICBjb25zdCBmb3JtYXR0ZWRDcmV3RHV0aWVzID0gYWN0aXZlRHV0aWVzLm1hcCgoZHV0eTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogZHV0eS50aXRsZSxcclxuICAgICAgICAgICAgICAgIHZhbHVlOiBkdXR5LmlkLFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICBzZXREdXRpZXMoZm9ybWF0dGVkQ3Jld0R1dGllcylcclxuICAgIH1cclxuICAgIGdldENyZXdEdXRpZXMoaGFuZGxlU2V0Q3Jld0R1dGllcylcclxuXHJcbiAgICAvLyBSZW5kZXIgZGVwYXJ0bWVudHMgcmVjdXJzaXZlbHkuXHJcbiAgICBjb25zdCByZW5kZXJEZXBhcnRtZW50ID0gKFxyXG4gICAgICAgIGRlcGFydG1lbnRzOiBhbnlbXSxcclxuICAgICAgICBwYXJlbnRJRDogbnVtYmVyID0gMCxcclxuICAgICAgICBkZXB0aDogbnVtYmVyID0gMCxcclxuICAgICk6IGFueVtdID0+IHtcclxuICAgICAgICByZXR1cm4gZGVwYXJ0bWVudHNcclxuICAgICAgICAgICAgLmZpbHRlcigoZGVwYXJ0bWVudDogYW55KSA9PiArZGVwYXJ0bWVudC5wYXJlbnRJRCA9PT0gcGFyZW50SUQpXHJcbiAgICAgICAgICAgIC5mbGF0TWFwKChkZXBhcnRtZW50OiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNoaWxkcmVuID0gcmVuZGVyRGVwYXJ0bWVudChcclxuICAgICAgICAgICAgICAgICAgICBkZXBhcnRtZW50cyxcclxuICAgICAgICAgICAgICAgICAgICArZGVwYXJ0bWVudC5pZCxcclxuICAgICAgICAgICAgICAgICAgICBkZXB0aCArIDEsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICBjb25zdCBpdGVtID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIC4uLmRlcGFydG1lbnQsXHJcbiAgICAgICAgICAgICAgICAgICAgbGV2ZWw6IGRlcHRoLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFtpdGVtLCAuLi5jaGlsZHJlbl1cclxuICAgICAgICAgICAgfSlcclxuICAgIH1cclxuICAgIGNvbnN0IFtyZWFkRGVwYXJ0bWVudHMsIHsgbG9hZGluZzogcmVhZERlcGFydG1lbnRzTG9hZGluZyB9XSA9IHVzZUxhenlRdWVyeShcclxuICAgICAgICBSZWFkRGVwYXJ0bWVudHMsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZERlcGFydG1lbnRzLm5vZGVzXHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSByZW5kZXJEZXBhcnRtZW50KGRhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0RGVwYXJ0bWVudHMoZm9ybWF0dGVkRGF0YSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5Q3Jld01lbWJlcnMgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG4gICAgY29uc3QgbG9hZERlcGFydG1lbnRzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGF3YWl0IHJlYWREZXBhcnRtZW50cygpXHJcbiAgICB9XHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgbG9hZERlcGFydG1lbnRzKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmddKVxyXG5cclxuICAgIC8vIFNldCB2ZXNzZWxzIGZyb20gdmVzc2VsIGJyaWVmIGxpc3QuXHJcbiAgICBjb25zdCBoYW5kbGVTZXRWZXNzZWxzID0gKHZlc3NlbHM6IGFueSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHZlc3NlbFNlbGVjdGlvbiA9IHZlc3NlbHMubWFwKCh2ZXNzZWw6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICByZXR1cm4geyBsYWJlbDogdmVzc2VsLnRpdGxlLCB2YWx1ZTogdmVzc2VsLmlkIH1cclxuICAgICAgICB9KVxyXG4gICAgICAgIHNldFZlc3NlbHModmVzc2VsU2VsZWN0aW9uKVxyXG4gICAgICAgIC8vbG9hZENyZXdNZW1iZXJzKClcclxuICAgIH1cclxuICAgIGdldFZlc3NlbEJyaWVmTGlzdChoYW5kbGVTZXRWZXNzZWxzKVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeUNyZXdNZW1iZXJzXSA9IHVzZUxhenlRdWVyeShDUkVXX0JSSUVGX0xJU1QsIHtcclxuICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICBvbkNvbXBsZXRlZDogKHJlc3BvbnNlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgaGFuZGxlU2V0Q3Jld01lbWJlcnMocmVzcG9uc2UucmVhZFNlYUxvZ3NNZW1iZXJzLm5vZGVzKVxyXG4gICAgICAgICAgICBzZXRQYWdlSW5mbyhyZXNwb25zZS5yZWFkU2VhTG9nc01lbWJlcnMucGFnZUluZm8pXHJcbiAgICAgICAgICAgIHJldHVybiByZXNwb25zZS5yZWFkU2VhTG9nc01lbWJlcnMubm9kZXNcclxuICAgICAgICB9LFxyXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5Q3Jld01lbWJlcnMgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuICAgIGNvbnN0IGhhbmRsZVNldENyZXdNZW1iZXJzID0gKGNyZXdNZW1iZXJzOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCB0cmFuc2Zvcm1lZENyZXdMaXN0ID0gR2V0Q3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMoXHJcbiAgICAgICAgICAgIGNyZXdNZW1iZXJzLFxyXG4gICAgICAgICAgICB2ZXNzZWxzLFxyXG4gICAgICAgIClcclxuICAgICAgICBzZXRDcmV3TGlzdCh0cmFuc2Zvcm1lZENyZXdMaXN0KVxyXG4gICAgfVxyXG5cclxuICAgIC8vIEZ1bmN0aW9uIHRvIGZpbHRlciBjcmV3IGxpc3QgYnkgdHJhaW5pbmcgc3RhdHVzXHJcbiAgICBjb25zdCBmaWx0ZXJDcmV3QnlUcmFpbmluZ1N0YXR1cyA9IChcclxuICAgICAgICBjcmV3TGlzdDogYW55W10sXHJcbiAgICAgICAgc3RhdHVzRmlsdGVyOiBzdHJpbmcgfCBudWxsLFxyXG4gICAgKSA9PiB7XHJcbiAgICAgICAgY29uc3QgY3Jld3MgPSBbLi4uY3Jld0xpc3RdLm1hcCgoY3JldzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGR1ZXMgPSBjcmV3LnRyYWluaW5nU3RhdHVzLmR1ZXNcclxuICAgICAgICAgICAgaWYgKGR1ZXMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4geyAuLi5jcmV3LCB0cmFpbmluZ1N0YXR1czogeyBsYWJlbDogJ0dvb2QnLCBkdWVzOiBbXSB9IH1cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChkdWVzLnNvbWUoKGR1ZTogYW55KSA9PiBkdWUuc3RhdHVzLmlzT3ZlcmR1ZSkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uY3JldyxcclxuICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1N0YXR1czoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogJ092ZXJkdWUnLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBkdWVzOiBkdWVzLmZpbHRlcigoZHVlOiBhbnkpID0+IGR1ZS5zdGF0dXMuaXNPdmVyZHVlKSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAuLi5jcmV3LFxyXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nU3RhdHVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAnICcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGR1ZXM6IGR1ZXMuZmlsdGVyKChkdWU6IGFueSkgPT4gIWR1ZS5zdGF0dXMuaXNPdmVyZHVlKSxcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgICBpZiAoIXN0YXR1c0ZpbHRlcikgcmV0dXJuIGNyZXdzXHJcbiAgICAgICAgcmV0dXJuIGNyZXdzLmZpbHRlcigoY3JldzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nU3RhdHVzID0gY3Jldy50cmFpbmluZ1N0YXR1cz8ubGFiZWxcclxuICAgICAgICAgICAgY29uc3QgZHVlcyA9IGNyZXcudHJhaW5pbmdTdGF0dXM/LmR1ZXMgfHwgW11cclxuXHJcbiAgICAgICAgICAgIGlmIChzdGF0dXNGaWx0ZXIgPT09ICdHb29kJykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRyYWluaW5nU3RhdHVzID09PSAnR29vZCdcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChzdGF0dXNGaWx0ZXIgPT09ICdPdmVyZHVlJykge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRyYWluaW5nU3RhdHVzID09PSAnT3ZlcmR1ZSdcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChzdGF0dXNGaWx0ZXIgPT09ICdEdWUgU29vbicpIHtcclxuICAgICAgICAgICAgICAgIC8vIER1ZSBTb29uIGlzIHJlcHJlc2VudGVkIGJ5IGFuIGVtcHR5IHN0cmluZyBsYWJlbCB3aXRoIGR1ZXNcclxuICAgICAgICAgICAgICAgIC8vIFRoaXMgaGFwcGVucyB3aGVuIHRoZXJlIGFyZSB0cmFpbmluZyBzZXNzaW9ucyBkdWUgd2l0aGluIDcgZGF5cyBidXQgbm90IG92ZXJkdWVcclxuICAgICAgICAgICAgICAgIHJldHVybiB0cmFpbmluZ1N0YXR1cyA9PT0gJyAnICYmIGR1ZXMubGVuZ3RoID4gMFxyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZVxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgLy8gR2V0IGZpbHRlcmVkIGNyZXcgbGlzdCBmb3IgZGlzcGxheVxyXG4gICAgY29uc3QgZmlsdGVyZWRDcmV3TGlzdCA9IGZpbHRlckNyZXdCeVRyYWluaW5nU3RhdHVzKFxyXG4gICAgICAgIGNyZXdMaXN0LFxyXG4gICAgICAgIHRyYWluaW5nU3RhdHVzRmlsdGVyLFxyXG4gICAgKVxyXG5cclxuICAgIGNvbnN0IGxvYWRDcmV3TWVtYmVycyA9IGFzeW5jIChcclxuICAgICAgICBzdGFydFBhZ2U6IG51bWJlciA9IDAsXHJcbiAgICAgICAgc2VhcmNoRmlsdGVyOiBhbnkgPSB7IC4uLmZpbHRlciB9LFxyXG4gICAgKSA9PiB7XHJcbiAgICAgICAgLypzZWFyY2hGaWx0ZXIuaXNBcmNoaXZlZCA9IHsgZXE6ICFzaG93QWN0aXZlVXNlcnMgfVxyXG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWx0ZXI6IFNlYXJjaEZpbHRlciA9IHtcclxuICAgICAgICAgICAgLi4uc2VhcmNoRmlsdGVyLFxyXG4gICAgICAgICAgICBpc0FyY2hpdmVkOiB7IGVxOiAhc2hvd0FjdGl2ZVVzZXJzIH0sXHJcbiAgICAgICAgfSovXHJcbiAgICAgICAgYXdhaXQgcXVlcnlDcmV3TWVtYmVycyh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgbGltaXQ6IGxpbWl0LFxyXG4gICAgICAgICAgICAgICAgb2Zmc2V0OiBzdGFydFBhZ2UgKiBsaW1pdCxcclxuICAgICAgICAgICAgICAgIGZpbHRlcjogc2VhcmNoRmlsdGVyLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgW211dGF0aW9uVXBkYXRlVXNlcl0gPSB1c2VNdXRhdGlvbihVUERBVEVfVVNFUiwge1xyXG4gICAgICAgIG9uQ29tcGxldGVkOiAoKSA9PiB7fSxcclxuICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdtdXRhdGlvblVwZGF0ZVVzZXIgZXJyb3InLCBlcnJvcilcclxuICAgICAgICB9LFxyXG4gICAgfSlcclxuXHJcbiAgICBjb25zdCBoYW5kbGVDcmV3RHV0eSA9IGFzeW5jIChkdXR5OiBhbnksIHVzZXI6IGFueSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHNlbGVjdGVkVXNlciA9IHtcclxuICAgICAgICAgICAgLi4uY3Jld0xpc3QuZmluZCgoY3JldzogYW55KSA9PiBjcmV3LklEID09PSB1c2VyLklEKSxcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgbmV3UHJpbWFyeUR1dHlJRCA9IGR1dHkudmFsdWVcclxuICAgICAgICBpZiAoc2VsZWN0ZWRVc2VyKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRDcmV3TGlzdCA9IGNyZXdMaXN0Lm1hcCgoY3JldzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoY3Jldy5JRCA9PT0gdXNlci5JRCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmNyZXcsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFByaW1hcnlEdXR5SUQ6IG5ld1ByaW1hcnlEdXR5SUQsXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNyZXdcclxuICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgc2V0Q3Jld0xpc3QodXBkYXRlZENyZXdMaXN0KVxyXG4gICAgICAgICAgICAvLyBVcGRhdGUgdXNlclxyXG4gICAgICAgICAgICBjb25zdCB2YXJpYWJsZXMgPSB7XHJcbiAgICAgICAgICAgICAgICBpbnB1dDoge1xyXG4gICAgICAgICAgICAgICAgICAgIGlkOiArdXNlci5pZCxcclxuICAgICAgICAgICAgICAgICAgICBwcmltYXJ5RHV0eUlEOiBuZXdQcmltYXJ5RHV0eUlELFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBhd2FpdCBtdXRhdGlvblVwZGF0ZVVzZXIoeyB2YXJpYWJsZXMgfSlcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlTmF2aWdhdGlvbkNsaWNrID0gKG5ld1BhZ2U6IGFueSkgPT4ge1xyXG4gICAgICAgIGlmIChuZXdQYWdlIDwgMCB8fCBuZXdQYWdlID09PSBwYWdlKSByZXR1cm5cclxuICAgICAgICBzZXRQYWdlKG5ld1BhZ2UpXHJcbiAgICAgICAgbG9hZENyZXdNZW1iZXJzKG5ld1BhZ2UpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaGFuZGxlRmlsdGVyT25DaGFuZ2UgPSAoeyB0eXBlLCBkYXRhIH06IGFueSkgPT4ge1xyXG4gICAgICAgIGludGVyZmFjZSBTZWFyY2hGaWx0ZXIge1xyXG4gICAgICAgICAgICBpc0FyY2hpdmVkPzogeyBlcTogYm9vbGVhbiB9XHJcbiAgICAgICAgICAgIHZlaGljbGVzPzogeyBpZDogeyBjb250YWlucz86IG51bWJlcjsgaW4/OiBudW1iZXJbXSB9IH1cclxuICAgICAgICAgICAgcHJpbWFyeUR1dHlJRD86IHsgZXE/OiBudW1iZXI7IGluPzogbnVtYmVyW10gfVxyXG4gICAgICAgICAgICBxPzogeyBjb250YWluczogc3RyaW5nIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IHNlYXJjaEZpbHRlcjogU2VhcmNoRmlsdGVyID0geyAuLi5maWx0ZXIgfVxyXG5cclxuICAgICAgICAvLyBIYW5kbGUgdHJhaW5pbmcgc3RhdHVzIGZpbHRlciBzZXBhcmF0ZWx5IHNpbmNlIGl0J3MgY2xpZW50LXNpZGVcclxuICAgICAgICBpZiAodHlwZSA9PT0gJ3RyYWluaW5nU3RhdHVzJykge1xyXG4gICAgICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLnZhbHVlKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRUcmFpbmluZ1N0YXR1c0ZpbHRlcihkYXRhLnZhbHVlKVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdTdGF0dXNGaWx0ZXIobnVsbClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4gLy8gRG9uJ3QgcmVsb2FkIGNyZXcgbWVtYmVycyBmb3IgY2xpZW50LXNpZGUgZmlsdGVyXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBpZiAodHlwZSA9PT0gJ3Zlc3NlbCcpIHtcclxuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YSkgJiYgZGF0YS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIudmVoaWNsZXMgPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWQ6IHsgaW46IGRhdGEubWFwKChpdGVtKSA9PiAraXRlbS52YWx1ZSkgfSxcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSBlbHNlIGlmIChkYXRhICYmICFBcnJheS5pc0FycmF5KGRhdGEpKSB7XHJcbiAgICAgICAgICAgICAgICBzZWFyY2hGaWx0ZXIudmVoaWNsZXMgPSB7IGlkOiB7IGNvbnRhaW5zOiArZGF0YS52YWx1ZSB9IH1cclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGRlbGV0ZSBzZWFyY2hGaWx0ZXIudmVoaWNsZXNcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodHlwZSA9PT0gJ2NyZXdEdXR5Jykge1xyXG4gICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkYXRhKSAmJiBkYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5wcmltYXJ5RHV0eUlEID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIGluOiBkYXRhLm1hcCgoaXRlbSkgPT4gK2l0ZW0udmFsdWUpLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGRhdGEgJiYgIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5wcmltYXJ5RHV0eUlEID0geyBlcTogK2RhdGEudmFsdWUgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci5wcmltYXJ5RHV0eUlEXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHR5cGUgPT09ICdrZXl3b3JkJykge1xyXG4gICAgICAgICAgICBpZiAoIWlzRW1wdHkoZGF0YS52YWx1ZSkpIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5xID0geyBjb250YWluczogZGF0YS52YWx1ZSB9XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBkZWxldGUgc2VhcmNoRmlsdGVyLnFcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAodHlwZSA9PT0gJ2lzQXJjaGl2ZWQnKSB7XHJcbiAgICAgICAgICAgIGlmIChkYXRhICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICAgICAgICAgIHNlYXJjaEZpbHRlci5pc0FyY2hpdmVkID0geyBlcTogIWRhdGEgfVxyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgZGVsZXRlIHNlYXJjaEZpbHRlci5pc0FyY2hpdmVkXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgc2V0RmlsdGVyKHNlYXJjaEZpbHRlcilcclxuICAgICAgICAvL3NldFBhZ2UoMClcclxuICAgICAgICBsb2FkQ3Jld01lbWJlcnMoMCwgc2VhcmNoRmlsdGVyKVxyXG4gICAgfVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0UGFnZSgwKVxyXG4gICAgICAgIGxvYWRDcmV3TWVtYmVycygwLCBmaWx0ZXIpXHJcbiAgICB9LCBbc2hvd0FjdGl2ZVVzZXJzLCBmaWx0ZXJdKVxyXG5cclxuICAgIC8vIENvbHVtbiBkZWZpbml0aW9ucyBmb3IgdGhlIERhdGFUYWJsZS5cclxuICAgIGNvbnN0IGNvbHVtbnM6IEV4dGVuZGVkQ29sdW1uRGVmPGFueSwgdW5rbm93bj5bXSA9IFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIk5hbWVcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyOiBhbnkgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIGNvbnN0IGZ1bGxOYW1lID0gYCR7Y3Jld01lbWJlci5maXJzdE5hbWV9ICR7Y3Jld01lbWJlci5zdXJuYW1lfWBcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBqdXN0aWZ5LXN0YXJ0IGl0ZW1zLWNlbnRlciBnYXAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyLnRyYWluaW5nU3RhdHVzPy5sYWJlbCA9PT0gJ092ZXJkdWUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2Rlc3RydWN0aXZlJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdzdWNjZXNzJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1pbi13LTMyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvY3Jldy9pbmZvP2lkPSR7Y3Jld01lbWJlci5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIml0ZW1zLWNlbnRlciB0cnVuY2F0ZSB0ZXh0LW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmdWxsTmFtZSB8fCAnLS0nfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWN1cmlvdXMtYmx1ZS00MDAgbGFwIHRleHQtWzEwcHhdXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdNZW1iZXIucHJpbWFyeUR1dHkudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBmdWxsTmFtZUEgPVxyXG4gICAgICAgICAgICAgICAgICAgIGAke3Jvd0E/Lm9yaWdpbmFsPy5maXJzdE5hbWV9ICR7cm93QT8ub3JpZ2luYWw/LnN1cm5hbWV9YCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCBmdWxsTmFtZUIgPVxyXG4gICAgICAgICAgICAgICAgICAgIGAke3Jvd0I/Lm9yaWdpbmFsPy5maXJzdE5hbWV9ICR7cm93Qj8ub3JpZ2luYWw/LnN1cm5hbWV9YCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gZnVsbE5hbWVBLmxvY2FsZUNvbXBhcmUoZnVsbE5hbWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3ZlaGljbGVzJyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ2xlZnQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIlZlc3NlbFwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXcgPSByb3cub3JpZ2luYWxcclxuXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBnYXAtMiBweS0yLjVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2NyZXcudmVoaWNsZXMubm9kZXMubWFwKCh2ZXNzZWw6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gR2V0IGNvbXBsZXRlIHZlc3NlbCBkYXRhIHdpdGggaWNvbiBpbmZvcm1hdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmVzc2VsV2l0aEljb24gPSBnZXRWZXNzZWxXaXRoSWNvbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWwuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17U3RyaW5nKHZlc3NlbC5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtc3RhcnQgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXAga2V5PXt2ZXNzZWwuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctZml0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxWZXNzZWxJY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw9e3Zlc3NlbFdpdGhJY29ufVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVzc2VsLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRpdGxlQSA9IHJvd0E/Lm9yaWdpbmFsPy52ZWhpY2xlcz8ubm9kZXM/LlswXT8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRpdGxlQiA9IHJvd0I/Lm9yaWdpbmFsPy52ZWhpY2xlcz8ubm9kZXM/LlswXT8udGl0bGUgfHwgJydcclxuXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGl0bGVBLmxvY2FsZUNvbXBhcmUodGl0bGVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3ByaW1hcnlEdXR5JyxcclxuICAgICAgICAgICAgY2VsbEFsaWdubWVudDogJ3JpZ2h0JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJQcmltYXJ5IGR1dHlcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAnbGFwdG9wJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgY3JldyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndoaXRlc3BhY2Utbm9ybWFsIHB4LTVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2NyZXcucHJpbWFyeUR1dHkudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPSByb3dBPy5vcmlnaW5hbD8ucHJpbWFyeUR1dHk/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPSByb3dCPy5vcmlnaW5hbD8ucHJpbWFyeUR1dHk/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RyYWluaW5nU3RhdHVzJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgdy1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiVHJhaW5pbmdcIiAvPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXcgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIHctZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Y3Jldy50cmFpbmluZ1N0YXR1cy5sYWJlbCA9PT0gJ092ZXJkdWUnID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiIHR5cGU9XCJjaXJjbGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jldy50cmFpbmluZ1N0YXR1cy5kdWVzLmxlbmd0aH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInN1Y2Nlc3NcIiB0eXBlPVwiY2lyY2xlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgXVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZURyb3Bkb3duQ2hhbmdlID0gKHR5cGU6IHN0cmluZywgZGF0YTogYW55KSA9PiB7XHJcbiAgICAgICAgaGFuZGxlRmlsdGVyT25DaGFuZ2UoeyB0eXBlLCBkYXRhIH0pXHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8PlxyXG4gICAgICAgICAgICA8TGlzdEhlYWRlclxyXG4gICAgICAgICAgICAgICAgaWNvbj17XHJcbiAgICAgICAgICAgICAgICAgICAgPFNlYWxvZ3NDcmV3SWNvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTEyIHctMTIgcmluZy0xIHAtMSByb3VuZGVkLWZ1bGwgYmctWyNmZmZdYH1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgdGl0bGU9XCJBbGwgY3Jld1wiXHJcbiAgICAgICAgICAgICAgICBhY3Rpb25zPXtcclxuICAgICAgICAgICAgICAgICAgICA8Q3Jld0ZpbHRlckFjdGlvbnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhkYXRhOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZURyb3Bkb3duQ2hhbmdlKCdpc0FyY2hpdmVkJywgZGF0YSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTE2XCI+XHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgY29sdW1ucz17Y29sdW1uc31cclxuICAgICAgICAgICAgICAgICAgICBkYXRhPXtmaWx0ZXJlZENyZXdMaXN0fVxyXG4gICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsyMH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlRmlsdGVyT25DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Lz5cclxuICAgIClcclxufVxyXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0vL1xyXG5leHBvcnQgY29uc3QgQ3Jld1RhYmxlID0gKHtcclxuICAgIGNyZXdMaXN0LFxyXG4gICAgdmVzc2VscyxcclxuICAgIGhhbmRsZUNyZXdEdXR5ID0gZmFsc2UsXHJcbiAgICBzaG93U3VybmFtZSxcclxufTogYW55KSA9PiB7XHJcbiAgICBjb25zdCBbaXNBZG1pbiwgc2V0SXNBZG1pbl0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtkZXBhcnRtZW50cywgc2V0RGVwYXJ0bWVudHNdID0gdXNlU3RhdGUoW10gYXMgYW55KVxyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgICBjb25zdCByZW5kZXJEZXBhcnRtZW50ID0gKFxyXG4gICAgICAgIGRlcGFydG1lbnRzOiBhbnlbXSxcclxuICAgICAgICBwYXJlbnRJRDogbnVtYmVyID0gMCxcclxuICAgICAgICBkZXB0aDogbnVtYmVyID0gMCxcclxuICAgICk6IGFueVtdID0+IHtcclxuICAgICAgICByZXR1cm4gZGVwYXJ0bWVudHNcclxuICAgICAgICAgICAgLmZpbHRlcigoZGVwYXJ0bWVudDogYW55KSA9PiArZGVwYXJ0bWVudC5wYXJlbnRJRCA9PT0gcGFyZW50SUQpXHJcbiAgICAgICAgICAgIC5mbGF0TWFwKChkZXBhcnRtZW50OiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNoaWxkcmVuID0gcmVuZGVyRGVwYXJ0bWVudChcclxuICAgICAgICAgICAgICAgICAgICBkZXBhcnRtZW50cyxcclxuICAgICAgICAgICAgICAgICAgICArZGVwYXJ0bWVudC5pZCxcclxuICAgICAgICAgICAgICAgICAgICBkZXB0aCArIDEsXHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICBjb25zdCBpdGVtID0ge1xyXG4gICAgICAgICAgICAgICAgICAgIC4uLmRlcGFydG1lbnQsXHJcbiAgICAgICAgICAgICAgICAgICAgbGV2ZWw6IGRlcHRoLFxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFtpdGVtLCAuLi5jaGlsZHJlbl1cclxuICAgICAgICAgICAgfSlcclxuICAgIH1cclxuICAgIGNvbnN0IFtyZWFkRGVwYXJ0bWVudHMsIHsgbG9hZGluZzogcmVhZERlcGFydG1lbnRzTG9hZGluZyB9XSA9IHVzZUxhenlRdWVyeShcclxuICAgICAgICBSZWFkRGVwYXJ0bWVudHMsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZERlcGFydG1lbnRzLm5vZGVzXHJcbiAgICAgICAgICAgICAgICBpZiAoZGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSByZW5kZXJEZXBhcnRtZW50KGRhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0RGVwYXJ0bWVudHMoZm9ybWF0dGVkRGF0YSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgb25FcnJvcjogKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ3F1ZXJ5Q3Jld01lbWJlcnMgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG4gICAgY29uc3QgbG9hZERlcGFydG1lbnRzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIGF3YWl0IHJlYWREZXBhcnRtZW50cygpXHJcbiAgICB9XHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgbG9hZERlcGFydG1lbnRzKClcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtpc0xvYWRpbmddKVxyXG4gICAgY29uc3QgY3Jld0xpc3RXaXRoVHJhaW5pbmdTdGF0dXMgPSBHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyhcclxuICAgICAgICBjcmV3TGlzdCxcclxuICAgICAgICB2ZXNzZWxzLFxyXG4gICAgKVxyXG4gICAgY29uc3QgdHJhbnNmb3JtZWRDcmV3TGlzdCA9IGNyZXdMaXN0V2l0aFRyYWluaW5nU3RhdHVzLm1hcChcclxuICAgICAgICAoY3Jld01lbWJlcjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkRHVlcyA9IGNyZXdNZW1iZXIudHJhaW5pbmdTdGF0dXMuZHVlcy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAoZHVlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY3Jld01lbWJlci52ZWhpY2xlcy5ub2Rlcy5zb21lKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAobm9kZTogYW55KSA9PiBub2RlLmlkID09PSBkdWUudmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkVHJhaW5pbmdTdGF0dXMgPSB7XHJcbiAgICAgICAgICAgICAgICAuLi5jcmV3TWVtYmVyLnRyYWluaW5nU3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgZHVlczogZmlsdGVyZWREdWVzLFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGlmIChmaWx0ZXJlZER1ZXMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgICAgICB1cGRhdGVkVHJhaW5pbmdTdGF0dXMubGFiZWwgPSAnR29vZCdcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXR1cm4geyAuLi5jcmV3TWVtYmVyLCB0cmFpbmluZ1N0YXR1czogdXBkYXRlZFRyYWluaW5nU3RhdHVzIH1cclxuICAgICAgICB9LFxyXG4gICAgKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKFxyXG4gICAgICAgICAgICB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJlxyXG4gICAgICAgICAgICB0eXBlb2Ygd2luZG93LmxvY2FsU3RvcmFnZSAhPT0gJ3VuZGVmaW5lZCdcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWluJylcclxuICAgICAgICAgICAgY29uc3QgYWRtaW4gPSByZXN1bHQgPT09ICd0cnVlJ1xyXG4gICAgICAgICAgICBzZXRJc0FkbWluKGFkbWluKVxyXG4gICAgICAgIH1cclxuICAgIH0sIFtdKVxyXG5cclxuICAgIGNvbnN0IGNvbHVtbnMgPSBjcmVhdGVDb2x1bW5zKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICcnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBjcmV3TWVtYmVyOiBhbnkgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yLjUgaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlci50cmFpbmluZ1N0YXR1cz8ubGFiZWwgIT09ICdHb29kJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdzdWNjZXNzJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlci5maXJzdE5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3TWVtYmVyLnN1cm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXJGYWxsYmFjaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL2NyZXcvaW5mbz9pZD0ke2NyZXdNZW1iZXIuaWR9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBwbC0yIHRleHQtbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdNZW1iZXIuZmlyc3ROYW1lIHx8ICctLSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dTdXJuYW1lID09IHRydWUgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJm5ic3A7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jld01lbWJlci5zdXJuYW1lIHx8ICctLSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJm5ic3A7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jld01lbWJlci5zdXJuYW1lIHx8ICctLSd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtoYW5kbGVDcmV3RHV0eSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbWQ6aGlkZGVuIGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXdNZW1iZXIudmVoaWNsZXMubm9kZXMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Jld01lbWJlci52ZWhpY2xlcy5ub2Rlcy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAodmVzc2VsOiBhbnksIGluZGV4OiBudW1iZXIpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoaW5kZXggPCAyKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt2ZXNzZWwuaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctbXV0ZWQgZm9udC1saWdodCByb3VuZGVkLWxnIHAtMiBib3JkZXIgbS0xIGJvcmRlci1ib3JkZXIgdGV4dC1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYXgtdy0zMiBvdmVyZmxvdy1oaWRkZW4gYmxvY2tcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL3Zlc3NlbC9pbmZvP2lkPSR7dmVzc2VsLmlkfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVzc2VsLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbmRleCA9PT0gMikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXIga2V5PXt2ZXNzZWwuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyVHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAreycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjcmV3TWVtYmVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLnZlaGljbGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLm5vZGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmxlbmd0aCAtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMn17JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9yZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudCBjbGFzc05hbWU9XCJwLTAgdy02NFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLWZ1bGwgYmctYmFja2dyb3VuZCByb3VuZGVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jld01lbWJlci52ZWhpY2xlcy5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zbGljZSgyKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHY6IGFueSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi5pZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLW11dGVkIGl0ZW1zLWNlbnRlciBvdmVyZmxvdy1hdXRvIHB4LTMgcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC92ZXNzZWwvaW5mbz9pZD0ke3YuaWR9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi50aXRsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndmVzc2VsJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoKSA9PiA8PntoYW5kbGVDcmV3RHV0eSAmJiAnVmVzc2VsJ308Lz4sXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyBhcyBjb25zdCxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgY3JldyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aGFuZGxlQ3Jld0R1dHkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jldy52ZWhpY2xlcy5ub2RlcyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmV3LnZlaGljbGVzLm5vZGVzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh2ZXNzZWw6IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbmRleCA8IDIpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3Zlc3NlbC5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1tdXRlZCBpbmxpbmUtYmxvY2sgZm9udC1saWdodCByb3VuZGVkLWxnIHAtMiBib3JkZXIgYm9yZGVyLWJvcmRlciBtLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtgL3Zlc3NlbC9pbmZvP2lkPSR7dmVzc2VsLmlkfWB9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmVzc2VsLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpbmRleCA9PT0gMikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXIga2V5PXt2ZXNzZWwuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyVHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAreycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjcmV3LnZlaGljbGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLm5vZGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmxlbmd0aCAtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMn17JyAnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9yZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudCBjbGFzc05hbWU9XCJwLTAgdy02NFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC1oLWZ1bGwgYmctYmFja2dyb3VuZCByb3VuZGVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y3Jldy52ZWhpY2xlcy5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5zbGljZSgyKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHY6IGFueSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi5pZFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGN1cnNvci1wb2ludGVyIGhvdmVyOmJnLW11dGVkIGl0ZW1zLWNlbnRlciBvdmVyZmxvdy1hdXRvIHB4LTMgcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17YC92ZXNzZWwvaW5mbz9pZD0ke3YuaWR9YH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdi50aXRsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAncHJpbWFyeUR1dHknLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdQcmltYXJ5IER1dHknLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXcgPSByb3cub3JpZ2luYWxcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdyYXAgdGV4dC1yaWdodCB3aGl0ZXNwYWNlLW5vcm1hbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Y3Jldy5wcmltYXJ5RHV0eS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd0cmFpbmluZ1N0YXR1cycsXHJcbiAgICAgICAgICAgIGhlYWRlcjogJ1RyYWluaW5nIFN0YXR1cycsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGNyZXcgPSByb3cub3JpZ2luYWxcclxuXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Y3Jldy50cmFpbmluZ1N0YXR1cy5sYWJlbCAhPT0gJ0dvb2QnID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXIgdHJpZ2dlclR5cGU9XCJob3ZlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyVHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2lyY2xlQWxlcnRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsxfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC05IHctOSB0ZXh0LWRlc3RydWN0aXZlIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1iYWNrZ3JvdW5kIHJvdW5kZWQgcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgd2hpdGVzcGFjZS1ub3dyYXAgZm9udC1tZWRpdW0gZm9jdXM6b3V0bGluZS1ub25lIGlubGluZS1ibG9jayByb3VuZGVkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NyZXcudHJhaW5pbmdTdGF0dXMuZHVlcy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW06IGFueSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZUluZGV4OiBudW1iZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2R1ZUluZGV4fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YCR7aXRlbS50cmFpbmluZ1R5cGUudGl0bGV9IC0gJHtpdGVtLnN0YXR1cy5sYWJlbH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1BvcG92ZXJDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzdWNjZXNzXCIgdHlwZT1cImNpcmNsZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVjayBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAnZGVwYXJ0bWVudHMnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICgpID0+IChcclxuICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAge2lzQWRtaW4gJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZURlcGFydG1lbnQnKSA9PT0gJ3RydWUnICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICdEZXBhcnRtZW50cyd9XHJcbiAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgY3JldyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNBZG1pbiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZURlcGFydG1lbnQnKSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAndHJ1ZScgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjcmV3LmRlcGFydG1lbnRzICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXcuZGVwYXJ0bWVudHMubm9kZXMubGVuZ3RoID4gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyZXcuZGVwYXJ0bWVudHMubm9kZXMubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChkZXBhcnRtZW50OiBhbnkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17ZGVwYXJ0bWVudC5pZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2AvZGVwYXJ0bWVudC9pbmZvP2lkPSR7ZGVwYXJ0bWVudC5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCB0ZXh0LW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlcGFydG1lbnRzLmZpbmQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChkZXB0OiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXB0LmlkID09PVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVwYXJ0bWVudC5pZCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApPy50aXRsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPk5vIGRlcGFydG1lbnRzIGZvdW5kPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgXSlcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgY29sdW1ucz17Y29sdW1uc31cclxuICAgICAgICAgICAgc2hvd1Rvb2xiYXI9e2ZhbHNlfVxyXG4gICAgICAgICAgICBkYXRhPXt0cmFuc2Zvcm1lZENyZXdMaXN0fVxyXG4gICAgICAgICAgICBwYWdlU2l6ZT17MjB9XHJcbiAgICAgICAgLz5cclxuICAgIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJDUkVXX0JSSUVGX0xJU1QiLCJSZWFkRGVwYXJ0bWVudHMiLCJ1c2VMYXp5UXVlcnkiLCJ1c2VNdXRhdGlvbiIsIkxpbmsiLCJVUERBVEVfVVNFUiIsImdldENyZXdEdXRpZXMiLCJHZXRDcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyIsImdldFZlc3NlbEJyaWVmTGlzdCIsImlzRW1wdHkiLCJ1c2VSb3V0ZXIiLCJ1c2VTaWRlYmFyIiwiQnV0dG9uIiwiY3JlYXRlQ29sdW1ucyIsIkRhdGFUYWJsZSIsIkRhdGFUYWJsZVNvcnRIZWFkZXIiLCJBdmF0YXIiLCJBdmF0YXJGYWxsYmFjayIsIkJhZGdlIiwiZ2V0Q3Jld0luaXRpYWxzIiwiVG9vbHRpcCIsIlRvb2x0aXBDb250ZW50IiwiVG9vbHRpcFRyaWdnZXIiLCJQb3BvdmVyIiwiUG9wb3ZlckNvbnRlbnQiLCJQb3BvdmVyVHJpZ2dlciIsIkNpcmNsZUFsZXJ0IiwiQ2hlY2siLCJTZWFsb2dzQ3Jld0ljb24iLCJDcmV3RmlsdGVyQWN0aW9ucyIsIkxpc3RIZWFkZXIiLCJWZXNzZWxJY29uIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJDcmV3TGlzdCIsInByb3BzIiwicm91dGVyIiwiY3Jld0xpc3QiLCJzZXRDcmV3TGlzdCIsInZlc3NlbHMiLCJzZXRWZXNzZWxzIiwidmVzc2VsSWNvbkRhdGEiLCJnZXRWZXNzZWxXaXRoSWNvbiIsInNob3dBY3RpdmVVc2VycyIsInNldFNob3dBY3RpdmVVc2VycyIsImlzTW9iaWxlIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZGVwYXJ0bWVudHMiLCJzZXREZXBhcnRtZW50cyIsImR1dGllcyIsInNldER1dGllcyIsImxpbWl0IiwicGFnZUluZm8iLCJzZXRQYWdlSW5mbyIsInRvdGFsQ291bnQiLCJoYXNOZXh0UGFnZSIsImhhc1ByZXZpb3VzUGFnZSIsInBhZ2UiLCJzZXRQYWdlIiwiZmlsdGVyIiwic2V0RmlsdGVyIiwiaXNBcmNoaXZlZCIsImVxIiwidHJhaW5pbmdTdGF0dXNGaWx0ZXIiLCJzZXRUcmFpbmluZ1N0YXR1c0ZpbHRlciIsImhhbmRsZVNldENyZXdEdXRpZXMiLCJjcmV3RHV0aWVzIiwiYWN0aXZlRHV0aWVzIiwiZHV0eSIsImFyY2hpdmVkIiwiZm9ybWF0dGVkQ3Jld0R1dGllcyIsIm1hcCIsImxhYmVsIiwidGl0bGUiLCJ2YWx1ZSIsImlkIiwicmVuZGVyRGVwYXJ0bWVudCIsInBhcmVudElEIiwiZGVwdGgiLCJkZXBhcnRtZW50IiwiZmxhdE1hcCIsImNoaWxkcmVuIiwiaXRlbSIsImxldmVsIiwicmVhZERlcGFydG1lbnRzIiwibG9hZGluZyIsInJlYWREZXBhcnRtZW50c0xvYWRpbmciLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJkYXRhIiwibm9kZXMiLCJmb3JtYXR0ZWREYXRhIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsImxvYWREZXBhcnRtZW50cyIsImhhbmRsZVNldFZlc3NlbHMiLCJ2ZXNzZWxTZWxlY3Rpb24iLCJ2ZXNzZWwiLCJxdWVyeUNyZXdNZW1iZXJzIiwiaGFuZGxlU2V0Q3Jld01lbWJlcnMiLCJyZWFkU2VhTG9nc01lbWJlcnMiLCJjcmV3TWVtYmVycyIsInRyYW5zZm9ybWVkQ3Jld0xpc3QiLCJmaWx0ZXJDcmV3QnlUcmFpbmluZ1N0YXR1cyIsInN0YXR1c0ZpbHRlciIsImNyZXdzIiwiY3JldyIsImR1ZXMiLCJ0cmFpbmluZ1N0YXR1cyIsImxlbmd0aCIsInNvbWUiLCJkdWUiLCJzdGF0dXMiLCJpc092ZXJkdWUiLCJmaWx0ZXJlZENyZXdMaXN0IiwibG9hZENyZXdNZW1iZXJzIiwic3RhcnRQYWdlIiwic2VhcmNoRmlsdGVyIiwidmFyaWFibGVzIiwib2Zmc2V0IiwibXV0YXRpb25VcGRhdGVVc2VyIiwiaGFuZGxlQ3Jld0R1dHkiLCJ1c2VyIiwic2VsZWN0ZWRVc2VyIiwiZmluZCIsIklEIiwibmV3UHJpbWFyeUR1dHlJRCIsInVwZGF0ZWRDcmV3TGlzdCIsIlByaW1hcnlEdXR5SUQiLCJpbnB1dCIsInByaW1hcnlEdXR5SUQiLCJoYW5kbGVOYXZpZ2F0aW9uQ2xpY2siLCJuZXdQYWdlIiwiaGFuZGxlRmlsdGVyT25DaGFuZ2UiLCJ0eXBlIiwiQXJyYXkiLCJpc0FycmF5IiwidmVoaWNsZXMiLCJpbiIsImNvbnRhaW5zIiwicSIsInVuZGVmaW5lZCIsImNvbHVtbnMiLCJhY2Nlc3NvcktleSIsImhlYWRlciIsImNvbHVtbiIsImNlbGwiLCJyb3ciLCJjcmV3TWVtYmVyIiwib3JpZ2luYWwiLCJmdWxsTmFtZSIsImZpcnN0TmFtZSIsInN1cm5hbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJzaXplIiwidmFyaWFudCIsImhyZWYiLCJwcmltYXJ5RHV0eSIsInNvcnRpbmdGbiIsInJvd0EiLCJyb3dCIiwiZnVsbE5hbWVBIiwiZnVsbE5hbWVCIiwibG9jYWxlQ29tcGFyZSIsImNlbGxBbGlnbm1lbnQiLCJ2ZXNzZWxXaXRoSWNvbiIsIlN0cmluZyIsInRpdGxlQSIsInRpdGxlQiIsImJyZWFrcG9pbnQiLCJ2YWx1ZUEiLCJ2YWx1ZUIiLCJoYW5kbGVEcm9wZG93bkNoYW5nZSIsImljb24iLCJhY3Rpb25zIiwib25DaGFuZ2UiLCJwYWdlU2l6ZSIsIkNyZXdUYWJsZSIsInNob3dTdXJuYW1lIiwiaXNBZG1pbiIsInNldElzQWRtaW4iLCJjcmV3TGlzdFdpdGhUcmFpbmluZ1N0YXR1cyIsImZpbHRlcmVkRHVlcyIsIm5vZGUiLCJ2ZXNzZWxJRCIsInVwZGF0ZWRUcmFpbmluZ1N0YXR1cyIsIndpbmRvdyIsImxvY2FsU3RvcmFnZSIsInJlc3VsdCIsImdldEl0ZW0iLCJhZG1pbiIsInNwYW4iLCJpbmRleCIsImFzQ2hpbGQiLCJzbGljZSIsInYiLCJ0cmlnZ2VyVHlwZSIsInN0cm9rZVdpZHRoIiwiZHVlSW5kZXgiLCJ0cmFpbmluZ1R5cGUiLCJkZXB0Iiwic2hvd1Rvb2xiYXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew/list.tsx\n"));

/***/ })

});